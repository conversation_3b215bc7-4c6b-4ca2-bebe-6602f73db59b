'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import VideoPlayer from './VideoPlayer';
import VideoSubmissionForm from './VideoSubmissionForm';
import { PlusIcon, VideoCameraIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { apiClient } from '@/lib/api';

interface Video {
  _id: string;
  title: string;
  description?: string;
  youtubeVideoId: string;
  thumbnailUrl: string;
  embedUrl: string;
  submittedBy: {
    _id: string;
    name: string;
  };
  reviewDate: string;
  tags: string[];
  viewCount: number;
}

interface PlaceVideosTabProps {
  placeId: string;
  placeName: string;
}

export default function PlaceVideosTab({ placeId, placeName }: PlaceVideosTabProps) {
  const { user } = useAuth();
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showSubmissionForm, setShowSubmissionForm] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  const fetchVideos = async (page = 1) => {
    try {
      setLoading(true);
      //const response = await fetch(`/api/videos/place/${placeId}?page=${page}&limit=6`);
      const response = await apiClient.getPlaceVideos(placeId, { page, limit: 6 });
      const data = await response.data;

      if (response.success) {
        setVideos(data.videos);
        setCurrentPage(data.pagination.currentPage);
        setTotalPages(data.pagination.totalPages);
        setTotalItems(data.pagination.totalItems);
        setError(null);
      } else {
        setError(data.error || 'Failed to load videos');
      }
    } catch (error) {
      console.error('Error fetching videos:', error);
      setError('Failed to load videos error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVideos();
  }, [placeId]);

  const handleViewIncrement = (videoId: string) => {
    setVideos(prev => prev.map(video => 
      video._id === videoId 
        ? { ...video, viewCount: video.viewCount + 1 }
        : video
    ));
  };

  const handleSubmissionSuccess = () => {
    toast.success('Video submitted successfully! It will appear after admin approval.');
    // Optionally refresh videos or show a message
  };

  const handlePageChange = (page: number) => {
    fetchVideos(page);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={() => fetchVideos()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Submit Button */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Travel Videos</h2>
          <p className="text-gray-600 mt-1">
            {totalItems > 0 
              ? `${totalItems} video${totalItems !== 1 ? 's' : ''} about ${placeName}`
              : `No videos yet for ${placeName}`
            }
          </p>
        </div>
        
        {user && (
          <button
            onClick={() => setShowSubmissionForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Submit Video</span>
          </button>
        )}
      </div>

      {/* Videos Grid */}
      {videos.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {videos.map((video) => (
              <VideoPlayer
                key={video._id}
                video={video}
                onViewIncrement={handleViewIncrement}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              <div className="flex space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-2 text-sm rounded-md ${
                      page === currentPage
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {page}
                  </button>
                ))}
              </div>

              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          )}
        </>
      ) : (
        /* Empty State */
        <div className="text-center py-12">
          <VideoCameraIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No videos yet</h3>
          <p className="text-gray-600 mb-6">
            Be the first to share a travel video about {placeName}!
          </p>
          {user ? (
            <button
              onClick={() => setShowSubmissionForm(true)}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <PlusIcon className="h-5 w-5" />
              <span>Submit First Video</span>
            </button>
          ) : (
            <p className="text-sm text-gray-500">
              Please log in to submit videos
            </p>
          )}
        </div>
      )}

      {/* Video Submission Form Modal */}
      <VideoSubmissionForm
        placeId={placeId}
        placeName={placeName}
        isOpen={showSubmissionForm}
        onClose={() => setShowSubmissionForm(false)}
        onSuccess={handleSubmissionSuccess}
      />
    </div>
  );
}
