// Validation script for cities.json
const fs = require('fs');
const path = require('path');

console.log('🔍 Validating cities.json...\n');

try {
  // Read and parse the JSON file
  const filePath = path.join(__dirname, 'cities.json');
  const jsonData = fs.readFileSync(filePath, 'utf8');
  const cities = JSON.parse(jsonData);

  console.log(`✅ JSON is valid and contains ${cities.length} cities\n`);

  // Validate each city
  cities.forEach((city, index) => {
    console.log(`🌍 Validating ${city.name}...`);
    
    // Required fields
    const requiredFields = ['name', 'slug', 'country', 'description', 'overview'];
    const missingFields = requiredFields.filter(field => !city[field]);
    
    if (missingFields.length > 0) {
      console.log(`  ❌ Missing required fields: ${missingFields.join(', ')}`);
    } else {
      console.log(`  ✅ All required fields present`);
    }

    // Validate coordinates
    if (city.coordinates && city.coordinates.latitude && city.coordinates.longitude) {
      console.log(`  ✅ Coordinates: ${city.coordinates.latitude}, ${city.coordinates.longitude}`);
    } else {
      console.log(`  ❌ Missing or invalid coordinates`);
    }

    // Validate images
    if (city.images && Array.isArray(city.images) && city.images.length > 0) {
      const primaryImages = city.images.filter(img => img.isPrimary);
      if (primaryImages.length === 1) {
        console.log(`  ✅ Images: ${city.images.length} total, 1 primary`);
      } else {
        console.log(`  ⚠️  Images: ${city.images.length} total, ${primaryImages.length} primary (should be 1)`);
      }
    } else {
      console.log(`  ❌ Missing or invalid images array`);
    }

    // Validate publication status
    if (city.isPublished === true && city.isFeatured === true) {
      console.log(`  ✅ Published and featured`);
    } else {
      console.log(`  ⚠️  Published: ${city.isPublished}, Featured: ${city.isFeatured}`);
    }

    console.log('');
  });

  // Summary
  console.log('📊 Summary:');
  console.log(`- Total cities: ${cities.length}`);
  console.log(`- Countries: ${[...new Set(cities.map(c => c.country))].join(', ')}`);
  console.log(`- Featured cities: ${cities.filter(c => c.isFeatured).length}`);
  console.log(`- Published cities: ${cities.filter(c => c.isPublished).length}`);
  
  const totalImages = cities.reduce((sum, city) => sum + (city.images ? city.images.length : 0), 0);
  console.log(`- Total images: ${totalImages}`);

  console.log('\n🎉 Validation complete! Ready for MongoDB import.');

} catch (error) {
  console.error('❌ Validation failed:', error.message);
  process.exit(1);
}
