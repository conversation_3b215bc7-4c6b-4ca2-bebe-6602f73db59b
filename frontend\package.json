{"name": "heritedge-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.0.0", "axios": "^1.6.0", "clsx": "^2.0.0", "next": "14.2.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8.4.31", "tailwindcss": "^3.4.0", "typescript": "^5"}}