{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitThis": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}