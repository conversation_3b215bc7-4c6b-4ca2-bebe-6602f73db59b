import { Response } from 'express';
import { validationResult } from 'express-validator';
import Place from '../models/Place';
import City from '../models/City';
import Review from '../models/Review';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

// @desc    Get all places
// @route   GET /api/places
// @access  Public
export const getPlaces = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 50; // Optimized default limit
  const skip = (page - 1) * limit;

  // Build query
  const query: any = { isPublished: true };

  if (req.query.city) {
    // Handle city by slug
    const cityDoc = await City.findOne({ slug: req.query.city });
    if (cityDoc) {
      query.city = cityDoc._id;
    }
  }
  
  if (req.query.category) {
    query.category = req.query.category;
  }
  
  if (req.query.search) {
    query.$text = { $search: req.query.search as string };
  }

  if (req.query.minRating) {
    query.averageRating = { $gte: parseFloat(req.query.minRating as string) };
  }

  if (req.query.priceRange) {
    query['pricing.priceRange'] = req.query.priceRange;
  }

  // Build sort
  let sort: any = { createdAt: -1 };
  if (req.query.sort) {
    const sortField = req.query.sort as string;
    sort = {};
    if (sortField.startsWith('-')) {
      sort[sortField.substring(1)] = -1;
    } else {
      sort[sortField] = 1;
    }
  }

  const places = await Place.find(query)
    .sort(sort)
    .skip(skip)
    .limit(limit)
    .populate('city', 'name slug country')
    .populate('createdBy', 'name')
    .select('-reviews -__v');

  const total = await Place.countDocuments(query);

  // Add rate limiting headers
  res.set({
    'X-RateLimit-Limit': '100',
    'X-RateLimit-Remaining': '99',
    'X-RateLimit-Reset': new Date(Date.now() + 60000).toISOString(),
    'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
  });

  res.json({
    success: true,
    data: {
      places,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get single place
// @route   GET /api/places/:slug
// @access  Public
export const getPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  const place = await Place.findOne({ slug: req.params.slug, isPublished: true })
    .populate('city', 'name slug country')
    .populate('createdBy', 'name avatar')
    .populate('reviews.user', 'name avatar');

  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  res.json({
    success: true,
    data: {
      place
    }
  });
});

// @desc    Create place
// @route   POST /api/places
// @access  Private
export const createPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  // Check if city exists
  const city = await City.findById(req.body.city);
  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Generate slug from name
  const slug = req.body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
  
  const placeData = {
    ...req.body,
    slug,
    createdBy: req.user!._id,
    lastUpdatedBy: req.user!._id
  };

  const place = await Place.create(placeData);

  // Add place to city's places array
  city.places.push(place._id);
  await city.save();

  res.status(201).json({
    success: true,
    message: 'Place created successfully',
    data: {
      place
    }
  });
});

// Placeholder implementations for other methods
export const updatePlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Update place - not implemented yet' });
});

export const deletePlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Delete place - not implemented yet' });
});

// @desc    Get place reviews
// @route   GET /api/places/:slug/reviews
// @access  Public
export const getPlaceReviews = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { slug } = req.params;

  // Find the place
  const place = await Place.findOne({ slug, isPublished: true });
  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  // Get reviews for this place (only approved reviews)
  const reviews = await Review.find({
    entityType: 'place',
    entityId: place._id,
    isApproved: true
  })
  .populate('user', 'name avatar')
  .sort({ createdAt: -1 });

  res.json({
    success: true,
    data: reviews
  });
});

// @desc    Add review to place
// @route   POST /api/places/:slug/reviews
// @access  Private
export const addReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { slug } = req.params;
  const { rating, comment } = req.body;

  // Find the place
  const place = await Place.findOne({ slug, isPublished: true });
  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  // Check if user already reviewed this place
  const existingReview = await Review.findOne({
    entityType: 'place',
    entityId: place._id,
    user: req.user!._id
  });

  if (existingReview) {
    return res.status(400).json({
      success: false,
      error: 'You have already reviewed this place'
    });
  }

  // Create review
  const review = await Review.create({
    entityType: 'place',
    entityId: place._id,
    user: req.user!._id,
    rating,
    comment
  });

  // Populate user data
  await review.populate('user', 'name avatar');

  // Update place's average rating (only approved reviews)
  const reviews = await Review.find({
    entityType: 'place',
    entityId: place._id,
    isApproved: true
  });

  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
    : 0;
  await Place.findByIdAndUpdate(place._id, {
    averageRating,
    totalReviews: reviews.length
  });

  res.status(201).json({
    success: true,
    message: 'Review added successfully',
    data: review
  });
});

// @desc    Update place review
// @route   PUT /api/places/:slug/reviews/:reviewId
// @access  Private (Review owner only)
export const updateReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { slug, reviewId } = req.params;
  const { rating, comment } = req.body;

  // Find the place
  const place = await Place.findOne({ slug, isPublished: true });
  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  // Find the review
  const review = await Review.findOne({
    _id: reviewId,
    entityType: 'place',
    entityId: place._id
  });

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  // Check if user owns the review
  if (review.user.toString() !== req.user!._id.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to update this review'
    });
  }

  // Update review
  review.rating = rating || review.rating;
  review.comment = comment || review.comment;
  await review.save();

  // Populate user data
  await review.populate('user', 'name avatar');

  // Update place's average rating (only approved reviews)
  const reviews = await Review.find({
    entityType: 'place',
    entityId: place._id,
    isApproved: true
  });

  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
    : 0;
  await Place.findByIdAndUpdate(place._id, {
    averageRating,
    totalReviews: reviews.length
  });

  res.json({
    success: true,
    message: 'Review updated successfully',
    data: review
  });
});

// @desc    Delete place review
// @route   DELETE /api/places/:slug/reviews/:reviewId
// @access  Private (Review owner, Admin, or Moderator)
export const deleteReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { slug, reviewId } = req.params;

  // Find the place
  const place = await Place.findOne({ slug, isPublished: true });
  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  // Find the review
  const review = await Review.findOne({
    _id: reviewId,
    entityType: 'place',
    entityId: place._id
  });

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  // Check if user owns the review or is admin/moderator
  const isOwner = review.user.toString() === req.user!._id.toString();
  const isAdminOrModerator = req.user!.role === 'admin' || req.user!.role === 'moderator';

  if (!isOwner && !isAdminOrModerator) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to delete this review'
    });
  }

  // Delete review
  await Review.findByIdAndDelete(reviewId);

  // Update place's average rating (only approved reviews)
  const reviews = await Review.find({
    entityType: 'place',
    entityId: place._id,
    isApproved: true
  });

  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
    : 0;

  await Place.findByIdAndUpdate(place._id, {
    averageRating,
    totalReviews: reviews.length
  });

  res.json({
    success: true,
    message: 'Review deleted successfully'
  });
});

export const bookmarkPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Bookmark place - not implemented yet' });
});

export const unbookmarkPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Unbookmark place - not implemented yet' });
});

export const getNearbyPlaces = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { latitude, longitude } = req.query;
  const radius = parseFloat(req.query.radius as string) || 10; // Default 10km
  const limit = parseInt(req.query.limit as string) || 20;

  const query: any = {
    isPublished: true,
    coordinates: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [parseFloat(longitude as string), parseFloat(latitude as string)]
        },
        $maxDistance: radius * 1000 // Convert km to meters
      }
    }
  };

  if (req.query.category) {
    query.category = req.query.category;
  }

  const places = await Place.find(query)
    .limit(limit)
    .populate('city', 'name slug')
    .select('-reviews -__v');

  res.json({
    success: true,
    data: {
      places
    }
  });
});
