import Link from 'next/link';
import {
  MapPinIcon,
  GlobeAltIcon,
  HeartIcon,
  StarIcon,
  EnvelopeIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  BuildingLibraryIcon
} from '@heroicons/react/24/outline';

const navigation = {
  explore: [
    { name: 'Cities', href: '/cities', icon: MapPinIcon },
    { name: 'Places', href: '/places', icon: GlobeAltIcon },
    { name: 'Restaurants', href: '/places?category=restaurant' },
    { name: 'Attractions', href: '/places?category=attraction' },
    { name: 'Hotels', href: '/places?category=hotel' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Our Story', href: '/story' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press', href: '/press' },
    { name: 'Blog', href: '/blog' },
  ],
  support: [
    { name: 'Help Center', href: '/help', icon: ChatBubbleLeftRightIcon },
    { name: 'Contact Us', href: '/contact', icon: EnvelopeIcon },
    { name: 'FAQ', href: '/faq' },
    { name: 'Community', href: '/community' },
    { name: 'Feedback', href: '/feedback' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'Licenses', href: '/licenses' },
  ],
  social: [
    {
      name: 'Facebook',
      href: '#',
      icon: (props: any) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
    {
      name: 'Instagram',
      href: '#',
      icon: (props: any) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M12.017 0C8.396 0 7.929.01 7.102.048 6.273.088 5.718.222 5.238.42a4.83 4.83 0 00-1.771 1.153A4.83 4.83 0 00.42 3.344c-.198.48-.332 1.035-.372 1.864C.01 6.035 0 6.502 0 10.124v3.753c0 3.621.01 4.088.048 4.915.04.829.174 1.384.372 1.864.2.675.52 1.248 1.153 1.771a4.83 4.83 0 001.771 1.153c.48.198 1.035.332 1.864.372.827.038 1.294.048 4.915.048h3.753c3.621 0 4.088-.01 4.915-.048.829-.04 1.384-.174 1.864-.372a4.83 4.83 0 001.771-1.153 4.83 4.83 0 001.153-1.771c.198-.48.332-1.035.372-1.864.038-.827.048-1.294.048-4.915V10.124c0-3.622-.01-4.089-.048-4.916-.04-.829-.174-1.384-.372-1.864a4.83 4.83 0 00-1.153-1.771A4.83 4.83 0 0019.777.42c-.48-.198-1.035-.332-1.864-.372C17.086.01 16.619 0 12.997 0H12.017zm-.481 2.164h.964c3.445 0 3.85.014 5.21.052.726.033 1.122.155 1.384.258.348.135.596.297.857.558.26.26.423.509.558.857.103.262.225.658.258 1.384.038 1.36.052 1.765.052 5.21 0 3.445-.014 3.85-.052 5.21-.033.726-.155 1.122-.258 1.384a2.31 2.31 0 01-.558.857 2.31 2.31 0 01-.857.558c-.262.103-.658.225-1.384.258-1.36.038-1.765.052-5.21.052-3.445 0-3.85-.014-5.21-.052-.726-.033-1.122-.155-1.384-.258a2.31 2.31 0 01-.857-.558 2.31 2.31 0 01-.558-.857c-.103-.262-.225-.658-.258-1.384-.038-1.36-.052-1.765-.052-5.21 0-3.445.014-3.85.052-5.21.033-.726.155-1.122.258-1.384.135-.348.297-.596.558-.857.26-.26.509-.423.857-.558.262-.103.658-.225 1.384-.258 1.19-.034 1.65-.043 4.246-.048zm8.408 2.188a1.44 1.44 0 11-2.88 0 1.44 1.44 0 012.88 0zM12.017 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12.017 16a4 4 0 110-8 4 4 0 010 8z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
    {
      name: 'Twitter',
      href: '#',
      icon: (props: any) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
        </svg>
      ),
    },
    {
      name: 'YouTube',
      href: '#',
      icon: (props: any) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
  ],
};

export function Footer() {
  return (
    <footer className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 border-t border-blue-500">
      <div className="mx-auto max-w-7xl px-4 py-3 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
          {/* Left side - Brand */}
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-amber-500 via-orange-500 to-red-500 flex items-center justify-center border border-white/30 shadow-lg">
                <BuildingLibraryIcon className="h-5 w-5 text-white drop-shadow-sm" />
              </div>
              <span className="text-lg font-bold text-white drop-shadow-sm">
                HeritEdge
              </span>
            </Link>
            <span className="text-sm text-blue-100">
              &copy; {new Date().getFullYear()} All rights reserved.
            </span>
          </div>

          {/* Right side - Links and Social */}
          <div className="flex items-center space-x-6">
            {/* Quick Links */}
            <div className="hidden md:flex items-center space-x-4 text-sm">
              <Link href="/cities" className="text-blue-100 hover:text-white transition-colors">
                Cities
              </Link>
              <Link href="/about" className="text-blue-100 hover:text-white transition-colors">
                About
              </Link>
              <Link href="/contact" className="text-blue-100 hover:text-white transition-colors">
                Contact
              </Link>
              <Link href="/privacy" className="text-blue-100 hover:text-white transition-colors">
                Privacy
              </Link>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-2">
              {navigation.social.slice(0, 3).map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="p-1.5 text-blue-200 hover:text-white hover:bg-white/10 rounded transition-all duration-200"
                >
                  <span className="sr-only">{item.name}</span>
                  <item.icon className="h-4 w-4" aria-hidden="true" />
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
