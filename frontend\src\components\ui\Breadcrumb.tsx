'use client';

import Link from 'next/link';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string }>;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

export function Breadcrumb({ items, className, showHome = true }: BreadcrumbProps) {
  const allItems = showHome 
    ? ([{ label: 'Home', href: '/', icon: HomeIcon } as BreadcrumbItem, ...items])
    : items;

  return (
    <nav className={cn('flex items-center space-x-1 text-sm', className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {allItems.map((item, index) => {
          const isLast = index === allItems.length - 1;
          const Icon = item.icon;

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRightIcon className="h-4 w-4 text-gray-400 mx-2" />
              )}
              
              {item.href && !isLast ? (
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors',
                    'hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-sm px-1 py-0.5'
                  )}
                >
                  {Icon && <Icon className="h-4 w-4" />}
                  <span>{item.label}</span>
                </Link>
              ) : (
                <span
                  className={cn(
                    'flex items-center space-x-1',
                    isLast || item.isActive
                      ? 'text-gray-900 font-medium'
                      : 'text-gray-600'
                  )}
                  aria-current={isLast ? 'page' : undefined}
                >
                  {Icon && <Icon className="h-4 w-4" />}
                  <span>{item.label}</span>
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

// Specialized breadcrumb for places with filters
interface PlacesBreadcrumbProps {
  city?: string;
  category?: string;
  search?: string;
  className?: string;
}

export function PlacesBreadcrumb({ city, category, search, className }: PlacesBreadcrumbProps) {
  const items: BreadcrumbItem[] = [
    { label: 'Places', href: '/places' }
  ];

  // Add city filter
  if (city && city !== 'all') {
    items.push({
      label: `${city.charAt(0).toUpperCase() + city.slice(1)} Places`,
      href: `/places?city=${city}`
    });
  }

  // Add category filter
  if (category && category !== 'all') {
    const categoryLabel = category === 'attraction' ? 'Attractions' :
                         category === 'restaurant' ? 'Restaurants' :
                         category === 'museum' ? 'Museums' :
                         category === 'religious' ? 'Religious Sites' :
                         category === 'historical' ? 'Historical Sites' :
                         category.charAt(0).toUpperCase() + category.slice(1);
    
    const href = city && city !== 'all' 
      ? `/places?city=${city}&category=${category}`
      : `/places?category=${category}`;
    
    items.push({
      label: categoryLabel,
      href
    });
  }

  // Add search filter
  if (search && search.trim() !== '') {
    const searchParams = new URLSearchParams();
    if (city && city !== 'all') searchParams.append('city', city);
    if (category && category !== 'all') searchParams.append('category', category);
    searchParams.append('search', search);
    
    items.push({
      label: `Search: "${search}"`,
      href: `/places?${searchParams.toString()}`,
      isActive: true
    });
  }

  return <Breadcrumb items={items} className={className} />;
}

// City breadcrumb for city pages
interface CityBreadcrumbProps {
  cityName: string;
  citySlug: string;
  section?: 'overview' | 'places' | 'restaurants' | 'hotels';
  className?: string;
}

export function CityBreadcrumb({ cityName, citySlug, section, className }: CityBreadcrumbProps) {
  const items: BreadcrumbItem[] = [
    { label: 'Cities', href: '/cities' },
    { label: cityName, href: `/cities/${citySlug}` }
  ];

  if (section && section !== 'overview') {
    const sectionLabel = section === 'places' ? 'Places' :
                        section === 'restaurants' ? 'Restaurants' :
                        section === 'hotels' ? 'Hotels' :
                        (section ? (section as string).charAt(0).toUpperCase() + (section as string).slice(1) : '');
    
    items.push({
      label: sectionLabel,
      isActive: true
    });
  }

  return <Breadcrumb items={items} className={className} />;
}

// Individual place breadcrumb
interface PlaceBreadcrumbProps {
  placeName: string;
  placeSlug: string;
  cityName: string;
  citySlug: string;
  category?: string;
  className?: string;
}

export function PlaceBreadcrumb({ 
  placeName, 
  placeSlug, 
  cityName, 
  citySlug, 
  category,
  className 
}: PlaceBreadcrumbProps) {
  const items: BreadcrumbItem[] = [
    { label: 'Cities', href: '/cities' },
    { label: cityName, href: `/cities/${citySlug}` },
    { label: 'Places', href: `/cities/${citySlug}#places` },
    { label: placeName, isActive: true }
  ];

  return <Breadcrumb items={items} className={className} />;
}
