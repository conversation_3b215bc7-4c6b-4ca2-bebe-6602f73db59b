'use client';

import { useState } from 'react';
import { StarIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface StarRatingProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  readonly?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
}

export function StarRating({ 
  rating, 
  onRatingChange, 
  readonly = false, 
  size = 'md',
  showValue = false,
  className = ''
}: StarRatingProps) {
  const [hoverRating, setHoverRating] = useState(0);
  const [isHovering, setIsHovering] = useState(false);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const handleClick = (value: number) => {
    if (!readonly && onRatingChange) {
      onRatingChange(value);
    }
  };

  const handleMouseEnter = (value: number) => {
    if (!readonly) {
      setHoverRating(value);
      setIsHovering(true);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverRating(0);
      setIsHovering(false);
    }
  };

  const displayRating = isHovering ? hoverRating : rating;

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => {
          const isFilled = star <= displayRating;
          const isPartial = star - 0.5 <= displayRating && displayRating < star;
          
          return (
            <button
              key={star}
              type="button"
              className={`${
                readonly ? 'cursor-default' : 'cursor-pointer hover:scale-110'
              } transition-transform duration-150 ${
                !readonly ? 'focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-50 rounded' : ''
              }`}
              onClick={() => handleClick(star)}
              onMouseEnter={() => handleMouseEnter(star)}
              onMouseLeave={handleMouseLeave}
              disabled={readonly}
            >
              {isFilled ? (
                <StarIconSolid 
                  className={`${sizeClasses[size]} text-yellow-400`} 
                />
              ) : isPartial ? (
                <div className="relative">
                  <StarIcon className={`${sizeClasses[size]} text-gray-300`} />
                  <div className="absolute inset-0 overflow-hidden w-1/2">
                    <StarIconSolid className={`${sizeClasses[size]} text-yellow-400`} />
                  </div>
                </div>
              ) : (
                <StarIcon 
                  className={`${sizeClasses[size]} ${
                    readonly ? 'text-gray-300' : 'text-gray-400 hover:text-yellow-400'
                  }`} 
                />
              )}
            </button>
          );
        })}
      </div>
      
      {showValue && (
        <span className="text-sm text-gray-600 ml-2">
          {rating.toFixed(1)} {rating !== 1 ? 'stars' : 'star'}
        </span>
      )}
    </div>
  );
}

// Display-only star rating for showing average ratings
export function DisplayStarRating({ 
  rating, 
  size = 'sm', 
  showValue = true,
  className = ''
}: {
  rating: number;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
}) {
  return (
    <StarRating 
      rating={rating}
      readonly={true}
      size={size}
      showValue={showValue}
      className={className}
    />
  );
}
