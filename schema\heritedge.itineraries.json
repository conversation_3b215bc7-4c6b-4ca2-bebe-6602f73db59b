[{"_id": {"$oid": "683f198199dd7f622d541aea"}, "title": "Trip to Paris", "description": "Need to explore the places specially Eiffel tower, taste the cuisine.", "user": {"$oid": "683ecafd0aefee668a579ca2"}, "city": {"$oid": "683c3d40fa0fe17f36a6390f"}, "duration": 3, "startDate": {"$date": "2025-06-18T00:00:00.000Z"}, "endDate": {"$date": "2025-06-20T00:00:00.000Z"}, "budget": "mid-range", "travelStyle": "solo", "interests": ["history", "culture", "food", "nature", "photography", "museums"], "days": [{"dayNumber": 1, "date": {"$date": "2025-06-18T00:00:00.000Z"}, "title": "Day 1", "places": [], "notes": "", "_id": {"$oid": "683f198199dd7f622d541aeb"}}, {"dayNumber": 2, "date": {"$date": "2025-06-19T00:00:00.000Z"}, "title": "Day 2", "places": [], "notes": "", "_id": {"$oid": "683f198199dd7f622d541aec"}}, {"dayNumber": 3, "date": {"$date": "2025-06-20T00:00:00.000Z"}, "title": "Day 3", "places": [], "notes": "", "_id": {"$oid": "683f198199dd7f622d541aed"}}], "accommodationPreference": "hotel", "transportPreference": "public", "specialRequests": "nothing special", "tags": ["history", "culture", "food", "nature", "photography", "museums"], "isPublic": true, "isTemplate": false, "likes": [{"$oid": "683ecafd0aefee668a579ca2"}], "saves": [], "views": 0, "totalRatings": 0, "status": "published", "isAIGenerated": false, "createdAt": {"$date": "2025-06-03T15:49:21.709Z"}, "updatedAt": {"$date": "2025-06-03T15:50:30.499Z"}, "__v": 1}]