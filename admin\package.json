{"name": "heritedge-admin-console", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "@headlessui/react": "^2.0.0", "@heroicons/react": "^2.0.0", "axios": "^1.6.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.0", "recharts": "^2.8.0", "@tanstack/react-table": "^8.10.0", "date-fns": "^2.30.0", "clsx": "^2.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}