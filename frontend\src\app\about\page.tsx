'use client';

import { 
  GlobeAltIcon, 
  HeartIcon, 
  UserGroupIcon,
  SparklesIcon,
  MapPinIcon,
  CameraIcon,
  BookOpenIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { Breadcrumb } from '@/components/ui/Breadcrumb';

export default function AboutPage() {
  const features = [
    {
      icon: GlobeAltIcon,
      title: 'Global Coverage',
      description: 'Discover amazing destinations from around the world, from bustling cities to hidden gems.'
    },
    {
      icon: MapPinIcon,
      title: 'Detailed Places',
      description: 'Get comprehensive information about attractions, restaurants, museums, and cultural sites.'
    },
    {
      icon: CameraIcon,
      title: 'Rich Media',
      description: 'High-quality images and detailed descriptions to help you plan your perfect trip.'
    },
    {
      icon: UserGroupIcon,
      title: 'Community Driven',
      description: 'Reviews and ratings from real travelers to help you make informed decisions.'
    },
    {
      icon: BookOpenIcon,
      title: 'Cultural Heritage',
      description: 'Learn about the history, culture, and traditions of each destination.'
    },
    {
      icon: ShieldCheckIcon,
      title: 'Trusted Information',
      description: 'Verified and up-to-date information you can rely on for your travels.'
    }
  ];

  const team = [
    {
      name: 'Travel Enthusiasts',
      role: 'Content Curators',
      description: 'Our team of passionate travelers curates the best destinations and experiences.'
    },
    {
      name: 'Local Experts',
      role: 'Cultural Guides',
      description: 'Local experts provide authentic insights into each destination\'s culture and heritage.'
    },
    {
      name: 'Tech Team',
      role: 'Platform Developers',
      description: 'Dedicated developers ensuring a smooth and enjoyable user experience.'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-white/10 backdrop-blur-sm rounded-full">
                <HeartIcon className="h-16 w-16 text-white" />
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              About HeritEdge
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Your gateway to discovering the world&apos;s most beautiful destinations and cultural heritage sites.
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb items={[{ label: 'About', isActive: true }]} />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Mission Section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Mission</h2>
          <p className="text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed">
            At HeritEdge, we believe that travel is more than just visiting places—it&apos;s about connecting with cultures,
            understanding history, and creating meaningful experiences. Our mission is to help travelers discover the 
            world&apos;s cultural heritage sites and hidden gems while providing authentic, detailed information to make
            every journey unforgettable.
          </p>
        </div>

        {/* Features Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">What Makes Us Special</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Story Section */}
        <div className="bg-white rounded-2xl p-8 md:p-12 shadow-sm mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
              <div className="space-y-4 text-gray-700">
                <p>
                  HeritEdge was born from a passion for travel and a deep appreciation for cultural heritage. 
                  We noticed that while there were many travel platforms, few focused specifically on the rich 
                  cultural and historical aspects that make destinations truly special.
                </p>
                <p>
                  Our platform brings together carefully curated information about cities, cultural sites, 
                  museums, religious landmarks, and local experiences. We work with local experts and 
                  cultural enthusiasts to ensure that every piece of information is authentic and valuable.
                </p>
                <p>
                  Whether you&apos;re planning a cultural expedition, seeking historical insights, or simply
                  looking to explore new destinations, HeritEdge is your trusted companion for meaningful travel.
                </p>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=600&q=80"
                alt="Cultural heritage site"
                className="rounded-xl shadow-lg w-full h-80 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Our Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <UserGroupIcon className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{member.name}</h3>
                <p className="text-blue-600 font-medium mb-3">{member.role}</p>
                <p className="text-gray-600">{member.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Values Section */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 md:p-12">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <SparklesIcon className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Authenticity</h3>
                  <p className="text-gray-600">We provide genuine, accurate information about destinations and cultural sites.</p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                  <HeartIcon className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Respect</h3>
                  <p className="text-gray-600">We honor and respect the cultural heritage and traditions of every destination.</p>
                </div>
              </div>
            </div>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                  <UserGroupIcon className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Community</h3>
                  <p className="text-gray-600">We build a community of travelers who share knowledge and experiences.</p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                  <GlobeAltIcon className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Sustainability</h3>
                  <p className="text-gray-600">We promote responsible travel that preserves cultural heritage for future generations.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
