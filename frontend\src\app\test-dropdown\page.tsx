'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

export default function TestDropdownPage() {
  const [isOpen, setIsOpen] = useState(false);

  const dropdownItems = [
    { name: 'All Places', href: '/places' },
    { name: 'Paris Places', href: '/places?city=paris' },
    { name: 'London Places', href: '/places?city=london' },
    { name: 'Museums', href: '/places?category=museum' },
    { name: 'Restaurants', href: '/places?category=restaurant' },
    { name: 'Attractions', href: '/places?category=attraction' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Dropdown Test Page</h1>
        
        <div className="bg-white p-8 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Test Places Dropdown</h2>
          
          <div className="relative inline-block">
            <button
              onClick={() => {
                console.log('Test dropdown clicked, current state:', isOpen);
                setIsOpen(!isOpen);
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <span>Places</span>
              <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
            </button>
            
            {isOpen && (
              <div className="absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50">
                {dropdownItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors border-b border-gray-100 last:border-b-0"
                    onClick={() => {
                      console.log('Dropdown item clicked:', item.href);
                      setIsOpen(false);
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <span>{item.name}</span>
                      {item.href.includes('city=') && (
                        <span className="text-xs text-blue-500 bg-blue-100 px-2 py-1 rounded-full">
                          City
                        </span>
                      )}
                      {item.href.includes('category=') && (
                        <span className="text-xs text-green-500 bg-green-100 px-2 py-1 rounded-full">
                          Category
                        </span>
                      )}
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
          
          <div className="mt-8">
            <h3 className="text-lg font-medium mb-4">Test Links Directly:</h3>
            <div className="space-y-2">
              {dropdownItems.map((item) => (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    {item.name} → {item.href}
                  </Link>
                </div>
              ))}
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-gray-100 rounded-lg">
            <h3 className="font-medium mb-2">Debug Info:</h3>
            <p>Dropdown Open: {isOpen ? 'Yes' : 'No'}</p>
            <p>Items Count: {dropdownItems.length}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
