name: <PERSON><PERSON> and Push Docker Images

on:
  push:
    branches:
      - master
      - develop
    tags:
      - 'v*'
  pull_request:
    branches:
      - master
      - develop

env:
  REGISTRY: docker.io
  # Change this to your Docker Hub username/organization
  DOCKER_HUB_USERNAME: ${{ secrets.DOCKER_HUB_USERNAME }}

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [admin, backend, frontend]
        include:
          - service: admin
            working-directory: ./admin
          - service: backend
            working-directory: ./backend
          - service: frontend
            working-directory: ./frontend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: ${{ matrix.working-directory }}/package-lock.json

      - name: Install dependencies
        working-directory: ${{ matrix.working-directory }}
        run: npm ci

      - name: Run linting
        working-directory: ${{ matrix.working-directory }}
        run: npm run lint --if-present || echo "No lint script found"

      - name: Run tests
        working-directory: ${{ matrix.working-directory }}
        run: npm test --if-present || echo "No test script found"

      - name: Build application
        working-directory: ${{ matrix.working-directory }}
        run: npm run build

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [admin, backend, frontend]
        include:
          - service: admin
            dockerfile: ./admin/Dockerfile
            context: ./admin
            port: 3001
          - service: backend
            dockerfile: ./backend/Dockerfile
            context: ./backend
            port: 5000
          - service: frontend
            dockerfile: ./frontend/Dockerfile
            context: ./frontend
            port: 3000

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ env.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.DOCKER_HUB_USERNAME }}/travelogix-${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.context }}
          file: ${{ matrix.dockerfile }}
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Image digest
        if: github.event_name != 'pull_request'
        run: echo ${{ steps.build.outputs.digest }}

  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.event_name != 'pull_request'
    strategy:
      matrix:
        service: [admin, backend, frontend]

    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.DOCKER_HUB_USERNAME }}/travelogix-${{ matrix.service }}:${{ github.ref_name }}
          format: 'sarif'
          output: 'trivy-results-${{ matrix.service }}.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results-${{ matrix.service }}.sarif'

  deploy-notification:
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: Deployment notification
        run: |
          echo "🚀 Docker images have been successfully built and pushed to Docker Hub!"
          echo "Images pushed:"
          echo "- ${{ env.DOCKER_HUB_USERNAME }}/travelogix-admin:latest"
          echo "- ${{ env.DOCKER_HUB_USERNAME }}/travelogix-backend:latest"
          echo "- ${{ env.DOCKER_HUB_USERNAME }}/travelogix-frontend:latest"
