{"name": "citytales-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "14.2.5", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "react-hot-toast": "^2.4.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.4.0", "postcss": "^8.4.31", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.2.5"}}