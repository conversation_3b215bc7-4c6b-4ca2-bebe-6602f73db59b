const { MongoClient } = require('mongodb');

async function makeItinerariesPublic() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('heritedge');
    const collection = db.collection('itineraries');
    
    // Update all itineraries to be public and published
    const result = await collection.updateMany(
      {}, // Update all documents
      {
        $set: {
          isPublic: true,
          status: 'published'
        }
      }
    );
    
    console.log(`Updated ${result.modifiedCount} itineraries to be public and published`);
    
    // Get count of public itineraries
    const publicCount = await collection.countDocuments({ isPublic: true, status: 'published' });
    console.log(`Total public itineraries: ${publicCount}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

makeItinerariesPublic();
