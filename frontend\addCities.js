// Quick script to add cities using the existing backend models
const mongoose = require('mongoose');

// Connect to the same database as the backend
mongoose.connect('mongodb://localhost:27017/citytales')
  .then(() => {
    console.log('✅ Connected to MongoDB');
    
    // Use the existing City model from the backend
    const City = require('./src/models/City');
    
    const cities = [
      {
        name: 'Paris',
        slug: 'paris',
        country: 'France',
        state: 'Île-de-France',
        description: 'The City of Light, known for its art, fashion, gastronomy, and culture.',
        overview: 'Paris, the capital of France, is a major European city and a global center for art, fashion, gastronomy and culture.',
        images: [{
          url: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=800&h=600&fit=crop',
          alt: 'Paris skyline with Eiffel Tower',
          isPrimary: true
        }],
        coordinates: { latitude: 48.8566, longitude: 2.3522 },
        population: 2161000,
        timezone: 'Europe/Paris',
        currency: 'EUR',
        languages: ['French'],
        climate: {
          type: 'Oceanic',
          bestTimeToVisit: 'April to June, September to October',
          averageTemperature: { summer: 25, winter: 7 }
        },
        transportation: {
          howToReach: {
            byAir: 'Charles de Gaulle Airport (CDG) and Orly Airport (ORY)',
            byRoad: 'Well connected by highways from all major European cities',
            byRail: 'Gare du Nord, Gare de Lyon - major railway stations'
          },
          localTransport: ['Metro', 'Bus', 'Tram', 'Vélib (bike sharing)'],
          airports: [
            { name: 'Charles de Gaulle', code: 'CDG', distance: 25 },
            { name: 'Orly', code: 'ORY', distance: 13 }
          ]
        },
        economy: { majorIndustries: ['Tourism', 'Fashion', 'Technology', 'Finance'] },
        culture: {
          festivals: ['Fête de la Musique', 'Nuit Blanche', 'Paris Fashion Week'],
          traditions: ['Café culture', 'Sunday markets', 'Evening strolls'],
          artAndCrafts: ['Fashion design', 'Perfumery', 'Culinary arts']
        },
        averageRating: 4.8,
        totalReviews: 15420,
        isPublished: true,
        isFeatured: true,
        tags: ['romantic', 'art', 'fashion', 'cuisine', 'historic']
      },
      {
        name: 'Tokyo',
        slug: 'tokyo',
        country: 'Japan',
        description: 'A bustling metropolis blending ultra-modern and traditional culture.',
        overview: 'Tokyo, Japan\'s busy capital, mixes the ultramodern and the traditional, from neon-lit skyscrapers to historic temples.',
        images: [{
          url: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=800&h=600&fit=crop',
          alt: 'Tokyo skyline at night',
          isPrimary: true
        }],
        coordinates: { latitude: 35.6762, longitude: 139.6503 },
        population: 13960000,
        timezone: 'Asia/Tokyo',
        currency: 'JPY',
        languages: ['Japanese'],
        climate: {
          type: 'Humid subtropical',
          bestTimeToVisit: 'March to May, September to November',
          averageTemperature: { summer: 30, winter: 10 }
        },
        transportation: {
          howToReach: {
            byAir: 'Narita International Airport (NRT) and Haneda Airport (HND)',
            byRoad: 'Connected by extensive highway network throughout Japan',
            byRail: 'Tokyo Station is a major hub for the Shinkansen (bullet train) network'
          },
          localTransport: ['JR Lines', 'Tokyo Metro', 'Private railways', 'Buses'],
          airports: [
            { name: 'Narita International', code: 'NRT', distance: 60 },
            { name: 'Haneda', code: 'HND', distance: 20 }
          ]
        },
        economy: { majorIndustries: ['Technology', 'Finance', 'Manufacturing', 'Tourism'] },
        culture: {
          festivals: ['Cherry Blossom Festival', 'Kanda Festival', 'Sanja Festival'],
          traditions: ['Tea ceremony', 'Kabuki theater', 'Sumo wrestling'],
          artAndCrafts: ['Origami', 'Calligraphy', 'Pottery', 'Woodworking']
        },
        averageRating: 4.7,
        totalReviews: 23150,
        isPublished: true,
        isFeatured: true,
        tags: ['modern', 'technology', 'culture', 'food', 'anime', 'temples']
      },
      {
        name: 'London',
        slug: 'london',
        country: 'United Kingdom',
        state: 'England',
        description: 'A historic capital blending royal heritage with modern innovation.',
        overview: 'London, the capital of England and the United Kingdom, is a 21st-century city with history stretching back to Roman times.',
        images: [{
          url: 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=800&h=600&fit=crop',
          alt: 'London skyline with Big Ben and Thames',
          isPrimary: true
        }],
        coordinates: { latitude: 51.5074, longitude: -0.1278 },
        population: 9540000,
        timezone: 'Europe/London',
        currency: 'GBP',
        languages: ['English'],
        climate: {
          type: 'Oceanic',
          bestTimeToVisit: 'May to September',
          averageTemperature: { summer: 23, winter: 7 }
        },
        transportation: {
          howToReach: {
            byAir: 'Heathrow (LHR), Gatwick (LGW), Stansted (STN), and Luton (LTN) airports',
            byRoad: 'Connected by M25 orbital motorway and major routes from across the UK',
            byRail: 'Multiple stations including King\'s Cross St. Pancras (Eurostar)'
          },
          localTransport: ['Underground (Tube)', 'Buses', 'Overground', 'Boris Bikes'],
          airports: [
            { name: 'Heathrow', code: 'LHR', distance: 24 },
            { name: 'Gatwick', code: 'LGW', distance: 48 }
          ]
        },
        economy: { majorIndustries: ['Finance', 'Technology', 'Creative Industries', 'Tourism'] },
        culture: {
          festivals: ['Notting Hill Carnival', 'London Film Festival', 'Pride in London'],
          traditions: ['Afternoon tea', 'Pub culture', 'Royal ceremonies'],
          artAndCrafts: ['Theatre', 'Literature', 'Fashion design', 'Contemporary art']
        },
        averageRating: 4.7,
        totalReviews: 38920,
        isPublished: true,
        isFeatured: true,
        tags: ['historic', 'royal', 'museums', 'theatre', 'multicultural']
      }
    ];

    // Clear existing cities and add new ones
    City.deleteMany({})
      .then(() => {
        console.log('🗑️ Cleared existing cities');
        return City.insertMany(cities);
      })
      .then((result) => {
        console.log(`✅ Added ${result.length} cities:`);
        result.forEach(city => {
          console.log(`- ${city.name} (${city.slug}) - ${city.country} ${city.isFeatured ? '⭐' : ''}`);
        });
        console.log('\n🎉 Cities added successfully!');
        console.log('🌐 Visit: http://localhost:3001/cities');
        process.exit(0);
      })
      .catch(error => {
        console.error('❌ Error:', error);
        process.exit(1);
      });
  })
  .catch(error => {
    console.error('❌ Connection error:', error);
    process.exit(1);
  });
