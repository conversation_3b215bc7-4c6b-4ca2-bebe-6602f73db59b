[{"_id": {"$oid": "68592069a2884686d43fd4e2"}, "title": "My Travel Blog", "description": "This is my first video about my holiday", "youtubeUrl": "https://www.youtube.com/watch?v=mnCOVvGKOWM&list=PLtL8hAfI7aMGReXu2HvRZcLWdlIhXZKbR&index=2", "youtubeVideoId": "mnCOVvGKOWM", "place": {"$oid": "683c4cd4fa0fe17f36a6392e"}, "submittedBy": {"$oid": "683ecafd0aefee668a579ca2"}, "status": "approved", "tags": ["travel", "holiday"], "viewCount": 0, "likes": 0, "isActive": true, "thumbnailUrl": "https://img.youtube.com/vi/mnCOVvGKOWM/maxresdefault.jpg", "submissionDate": {"$date": "2025-06-23T09:37:45.316Z"}, "createdAt": {"$date": "2025-06-23T09:37:45.317Z"}, "updatedAt": {"$date": "2025-06-23T16:57:56.501Z"}, "__v": 0, "reviewDate": {"$date": "2025-06-23T16:57:56.497Z"}, "reviewNotes": "ok", "reviewedBy": {"$oid": "684156bc66cda045cc14c80f"}}, {"_id": {"$oid": "685991741bfd18c0ddd1c348"}, "title": "My Swiss Trip", "description": "My Swiss trip in 2020", "youtubeUrl": "https://www.youtube.com/watch?v=dw-Pgawac1k", "youtubeVideoId": "dw-Pgawac1k", "place": {"$oid": "683c4cd4fa0fe17f36a6392e"}, "submittedBy": {"$oid": "683ecafd0aefee668a579ca2"}, "status": "approved", "tags": ["swi<PERSON><PERSON><PERSON>", "interlaken", "mount titlis"], "viewCount": 0, "likes": 0, "isActive": true, "thumbnailUrl": "https://img.youtube.com/vi/dw-Pgawac1k/maxresdefault.jpg", "submissionDate": {"$date": "2025-06-23T17:40:04.011Z"}, "createdAt": {"$date": "2025-06-23T17:40:04.017Z"}, "updatedAt": {"$date": "2025-06-23T17:40:40.341Z"}, "__v": 0, "reviewDate": {"$date": "2025-06-23T17:40:40.338Z"}, "reviewNotes": "ok", "reviewedBy": {"$oid": "684156bc66cda045cc14c80f"}}, {"_id": {"$oid": "68599354c53a2b4ff81b44ea"}, "title": "My world Trip", "description": "World Trip", "youtubeUrl": "https://www.youtube.com/watch?v=TG67BR9OSok&list=PLtL8hAfI7aMGReXu2HvRZcLWdlIhXZKbR&index=1", "youtubeVideoId": "TG67BR9OSok", "place": {"$oid": "683c50bdfa0fe17f36a63942"}, "submittedBy": {"$oid": "683ecafd0aefee668a579ca2"}, "status": "approved", "tags": ["netherlands", "explore"], "viewCount": 0, "likes": 0, "isActive": true, "thumbnailUrl": "https://img.youtube.com/vi/TG67BR9OSok/maxresdefault.jpg", "submissionDate": {"$date": "2025-06-23T17:48:04.048Z"}, "createdAt": {"$date": "2025-06-23T17:48:04.050Z"}, "updatedAt": {"$date": "2025-06-23T17:48:21.745Z"}, "__v": 0, "reviewDate": {"$date": "2025-06-23T17:48:21.744Z"}, "reviewNotes": "ok", "reviewedBy": {"$oid": "684156bc66cda045cc14c80f"}}, {"_id": {"$oid": "685a64f1ca684c005a49e03b"}, "title": "Visit to Arc de Triomphe", "description": "visit to Arc de Triomphe", "youtubeUrl": "https://www.youtube.com/watch?v=R8eb_a7gEcs", "youtubeVideoId": "R8eb_a7gEcs", "place": {"$oid": "683c4cd4fa0fe17f36a63932"}, "submittedBy": {"$oid": "684ffe8313708d96e39af56a"}, "status": "approved", "tags": ["monument", "paris"], "viewCount": 0, "likes": 0, "isActive": true, "thumbnailUrl": "https://img.youtube.com/vi/R8eb_a7gEcs/maxresdefault.jpg", "submissionDate": {"$date": "2025-06-24T08:42:25.619Z"}, "createdAt": {"$date": "2025-06-24T08:42:25.622Z"}, "updatedAt": {"$date": "2025-06-24T08:42:52.930Z"}, "__v": 0, "reviewDate": {"$date": "2025-06-24T08:42:52.929Z"}, "reviewNotes": "ok", "reviewedBy": {"$oid": "684156bc66cda045cc14c80f"}}]