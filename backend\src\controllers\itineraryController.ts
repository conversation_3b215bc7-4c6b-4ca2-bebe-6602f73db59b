import { Response } from 'express';
import { validationResult } from 'express-validator';
import { Itinerary } from '../models/Itinerary';
import User from '../models/User';
import Place from '../models/Place';
import City from '../models/City';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

// @desc    Get all itineraries with filtering
// @route   GET /api/itineraries
// @access  Public
export const getItineraries = asyncHandler(async (req: AuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const skip = (page - 1) * limit;

  const filter: any = { isPublic: true, status: 'published' };

  // Add filters
  if (req.query.city) {
    filter.city = req.query.city;
  }
  if (req.query.travelStyle) {
    filter.travelStyle = req.query.travelStyle;
  }
  if (req.query.duration) {
    filter.duration = parseInt(req.query.duration as string);
  }
  if (req.query.search) {
    filter.$or = [
      { title: { $regex: req.query.search, $options: 'i' } },
      { description: { $regex: req.query.search, $options: 'i' } },
      { tags: { $in: [new RegExp(req.query.search as string, 'i')] } }
    ];
  }

  // Sort options
  let sort: any = { createdAt: -1 };
  if (req.query.sort === 'popular') {
    sort = { likes: -1, views: -1 };
  } else if (req.query.sort === 'rating') {
    sort = { rating: -1 };
  }

  const itineraries = await Itinerary.find(filter)
    .populate('user', 'name avatar')
    .populate('city', 'name slug country images')
    .sort(sort)
    .skip(skip)
    .limit(limit);

  const total = await Itinerary.countDocuments(filter);

  res.json({
    success: true,
    data: {
      itineraries,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get single itinerary
// @route   GET /api/itineraries/:id
// @access  Public (if public) / Private (if own)
export const getItinerary = asyncHandler(async (req: AuthRequest, res: Response) => {
  const itinerary = await Itinerary.findById(req.params.id)
    .populate('user', 'name avatar profile')
    .populate('city', 'name slug country images')
    .populate('days.places.place', 'name slug images category address operatingHours pricing');

  if (!itinerary) {
    return res.status(404).json({
      success: false,
      error: 'Itinerary not found'
    });
  }

  // Check if user can access this itinerary
  const isOwner = req.user && req.user._id.toString() === itinerary.user._id.toString();
  if (!itinerary.isPublic && !isOwner) {
    return res.status(403).json({
      success: false,
      error: 'This itinerary is private'
    });
  }

  // Increment views if not owner
  if (!isOwner) {
    itinerary.views += 1;
    await itinerary.save();
  }

  res.json({
    success: true,
    data: {
      itinerary
    }
  });
});

// @desc    Create new itinerary
// @route   POST /api/itineraries
// @access  Private
export const createItinerary = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const {
    title,
    description,
    cityId,
    duration,
    startDate,
    endDate,
    budget,
    travelStyle,
    interests,
    accommodationPreference,
    transportPreference,
    specialRequests,
    days,
    tags,
    isPublic
  } = req.body;

  // Verify city exists
  const cityExists = await City.findById(cityId);
  if (!cityExists) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Create empty days if not provided
  let dayPlans = days;
  if (!dayPlans || dayPlans.length === 0) {
    dayPlans = [];
    const start = new Date(startDate);

    for (let i = 0; i < duration; i++) {
      const dayDate = new Date(start);
      dayDate.setDate(start.getDate() + i);

      dayPlans.push({
        dayNumber: i + 1,
        date: dayDate,
        title: `Day ${i + 1}`,
        places: [],
        notes: ''
      });
    }
  }

  // Verify all places exist if provided
  if (dayPlans && dayPlans.length > 0) {
    for (const day of dayPlans) {
      if (day.places && day.places.length > 0) {
        for (const placeItem of day.places) {
          const place = await Place.findById(placeItem.place);
          if (!place) {
            return res.status(404).json({
              success: false,
              error: `Place not found: ${placeItem.place}`
            });
          }
        }
      }
    }
  }

  const itinerary = await Itinerary.create({
    title,
    description,
    user: req.user!._id,
    city: cityId,
    duration,
    startDate: startDate ? new Date(startDate) : new Date(),
    endDate: endDate ? new Date(endDate) : new Date(),
    budget,
    travelStyle,
    interests: interests || [],
    accommodationPreference,
    transportPreference,
    specialRequests,
    days: dayPlans,
    tags: tags || interests || [],
    isPublic: isPublic || false, // Keep itineraries private
    status: 'draft' // Default to draft status
  });

  // Add to user's itineraries
  await User.findByIdAndUpdate(req.user!._id, {
    $push: { itineraries: itinerary._id }
  });

  const populatedItinerary = await Itinerary.findById(itinerary._id)
    .populate('city', 'name slug country')
    .populate('days.places.place', 'name slug images');

  res.status(201).json({
    success: true,
    message: 'Itinerary created successfully',
    data: {
      itinerary: populatedItinerary
    }
  });
});

// @desc    Update itinerary
// @route   PUT /api/itineraries/:id
// @access  Private (owner only)
export const updateItinerary = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const itinerary = await Itinerary.findById(req.params.id);
  if (!itinerary) {
    return res.status(404).json({
      success: false,
      error: 'Itinerary not found'
    });
  }

  // Check ownership
  if (itinerary.user.toString() !== req.user!._id.toString()) {
    return res.status(403).json({
      success: false,
      error: 'You can only update your own itineraries'
    });
  }

  const allowedFields = ['title', 'description', 'duration', 'budget', 'travelStyle', 'days', 'tags', 'isPublic', 'status'];
  const updates: any = {};

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      updates[key] = req.body[key];
    }
  });

  const updatedItinerary = await Itinerary.findByIdAndUpdate(
    req.params.id,
    updates,
    { new: true, runValidators: true }
  ).populate('city', 'name slug country')
   .populate('days.places.place', 'name slug images');

  res.json({
    success: true,
    message: 'Itinerary updated successfully',
    data: {
      itinerary: updatedItinerary
    }
  });
});

// @desc    Delete itinerary
// @route   DELETE /api/itineraries/:id
// @access  Private (owner only)
export const deleteItinerary = asyncHandler(async (req: AuthRequest, res: Response) => {
  const itinerary = await Itinerary.findById(req.params.id);
  if (!itinerary) {
    return res.status(404).json({
      success: false,
      error: 'Itinerary not found'
    });
  }

  // Check ownership
  if (itinerary.user.toString() !== req.user!._id.toString()) {
    return res.status(403).json({
      success: false,
      error: 'You can only delete your own itineraries'
    });
  }

  await Itinerary.findByIdAndDelete(req.params.id);

  // Remove from user's itineraries
  await User.findByIdAndUpdate(req.user!._id, {
    $pull: { itineraries: req.params.id }
  });

  res.json({
    success: true,
    message: 'Itinerary deleted successfully'
  });
});

// @desc    Get user's itineraries
// @route   GET /api/itineraries/user/:userId
// @access  Private (own) / Public (if public profile)
export const getUserItineraries = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { userId } = req.params;

  // Validate userId parameter
  if (!userId || userId === 'undefined') {
    return res.status(400).json({
      success: false,
      error: 'Valid user ID is required'
    });
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const skip = (page - 1) * limit;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  const isOwnProfile = req.user && req.user._id.toString() === userId;
  
  let filter: any = { user: userId };
  
  // If not own profile, only show public itineraries
  if (!isOwnProfile) {
    filter.isPublic = true;
    filter.status = 'published';
  }

  const itineraries = await Itinerary.find(filter)
    .populate('city', 'name slug country images')
    .sort({ updatedAt: -1 })
    .skip(skip)
    .limit(limit);

  const total = await Itinerary.countDocuments(filter);

  res.json({
    success: true,
    data: {
      itineraries,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Toggle itinerary like
// @route   POST /api/itineraries/:id/like
// @access  Private
export const toggleItineraryLike = asyncHandler(async (req: AuthRequest, res: Response) => {
  const itinerary = await Itinerary.findById(req.params.id);
  if (!itinerary) {
    return res.status(404).json({
      success: false,
      error: 'Itinerary not found'
    });
  }

  const userId = req.user!._id;
  const isLiked = itinerary.likes.includes(userId);

  if (isLiked) {
    itinerary.likes = itinerary.likes.filter(id => id.toString() !== userId.toString());
  } else {
    itinerary.likes.push(userId);
  }

  await itinerary.save();

  res.json({
    success: true,
    data: {
      isLiked: !isLiked,
      totalLikes: itinerary.likes.length,
      message: isLiked ? 'Itinerary unliked' : 'Itinerary liked'
    }
  });
});
