// Test script to verify admin configuration in Kubernetes
const axios = require('axios');

const ADMIN_URL = 'http://localhost:30002';
const BACKEND_URL = 'http://localhost:30001/api';

async function testAdminConfig() {
  console.log('🧪 Testing Admin Kubernetes Configuration...\n');

  try {
    // Test 1: Check if admin is accessible
    console.log('1. Testing admin accessibility...');
    const adminResponse = await axios.get(ADMIN_URL, { timeout: 5000 });
    console.log('✅ Admin is accessible at:', ADMIN_URL);
    console.log('   Status:', adminResponse.status);

    // Test 2: Check if backend is accessible via NodePort
    console.log('\n2. Testing backend accessibility via NodePort...');
    const backendResponse = await axios.get(`${BACKEND_URL}/health`, { timeout: 5000 });
    console.log('✅ Backend is accessible at:', BACKEND_URL);
    console.log('   Status:', backendResponse.status);
    console.log('   Response:', backendResponse.data);

    // Test 3: Check admin configuration by looking for API calls
    console.log('\n3. Testing admin API configuration...');
    console.log('   Expected admin to use:', BACKEND_URL);
    console.log('   This should be configured in the admin environment variables');

    console.log('\n🎉 All tests passed! Admin should now correctly access backend via Kubernetes NodePort.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the test
testAdminConfig();
