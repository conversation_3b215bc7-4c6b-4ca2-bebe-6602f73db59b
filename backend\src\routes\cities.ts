import express from 'express';
import { body, query, param } from 'express-validator';
import {
  getCities,
  getCity,
  createCity,
  updateCity,
  deleteCity,
  getFeaturedCities,
  getGlobalStats,
  getCityPlaces,
  bookmarkCity,
  unbookmarkCity,
  getCityStats,
  addCityReview,
  updateCityReview,
  deleteCityReview,
  getCityReviews
} from '../controllers/cityController';
import { protect, authorize, optionalAuth } from '../middleware/auth';

const router = express.Router();

// @route   GET /api/cities
// @desc    Get all cities with filtering, sorting, and pagination
// @access  Public
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('country')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Country must be at least 2 characters'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Search term must be at least 2 characters'),
  query('sort')
    .optional()
    .isIn(['name', '-name', 'averageRating', '-averageRating', 'createdAt', '-createdAt'])
    .withMessage('Invalid sort parameter')
], getCities);

// @route   GET /api/cities/featured
// @desc    Get featured cities
// @access  Public
router.get('/featured', getFeaturedCities);

// @route   GET /api/cities/stats/global
// @desc    Get global statistics
// @access  Public
router.get('/stats/global', getGlobalStats);

// @route   POST /api/cities/seed
// @desc    Seed cities (temporary endpoint for development)
// @access  Public (temporary)
router.post('/seed', [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('City name must be between 2 and 100 characters'),
  body('country')
    .trim()
    .isLength({ min: 2 })
    .withMessage('Country is required'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('coordinates.latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('coordinates.longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180')
], createCity);

// @route   GET /api/cities/:slug
// @desc    Get single city by slug
// @access  Public
router.get('/:slug', [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format')
], optionalAuth, getCity);

// @route   POST /api/cities
// @desc    Create new city
// @access  Private (Admin/Moderator only)
router.post('/', protect, authorize('admin', 'moderator'), [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('City name must be between 2 and 100 characters'),
  body('country')
    .trim()
    .isLength({ min: 2 })
    .withMessage('Country is required'),
  body('description')
    .trim()
    .isLength({ min: 50, max: 2000 })
    .withMessage('Description must be between 50 and 2000 characters'),
  body('overview')
    .trim()
    .isLength({ min: 100, max: 5000 })
    .withMessage('Overview must be between 100 and 5000 characters'),
  body('coordinates.latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('coordinates.longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  body('timezone')
    .notEmpty()
    .withMessage('Timezone is required'),
  body('currency')
    .notEmpty()
    .withMessage('Currency is required'),
  body('languages')
    .isArray({ min: 1 })
    .withMessage('At least one language is required'),
  body('seoMetadata.title')
    .trim()
    .isLength({ min: 10, max: 60 })
    .withMessage('SEO title must be between 10 and 60 characters'),
  body('seoMetadata.description')
    .trim()
    .isLength({ min: 50, max: 160 })
    .withMessage('SEO description must be between 50 and 160 characters')
], createCity);

// @route   PUT /api/cities/:slug
// @desc    Update city
// @access  Private (Admin/Moderator only)
router.put('/:slug', protect, authorize('admin', 'moderator'), [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format'),
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('City name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 50, max: 2000 })
    .withMessage('Description must be between 50 and 2000 characters'),
  body('coordinates.latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('coordinates.longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180')
], updateCity);

// @route   DELETE /api/cities/:slug
// @desc    Delete city
// @access  Private (Admin only)
router.delete('/:slug', protect, authorize('admin'), [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format')
], deleteCity);

// @route   GET /api/cities/:slug/places
// @desc    Get all places in a city
// @access  Public
router.get('/:slug/places', [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format'),
  query('category')
    .optional()
    .isIn(['historical', 'attraction', 'restaurant', 'hotel', 'shopping', 'entertainment', 'nature', 'religious', 'museum', 'other'])
    .withMessage('Invalid category'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getCityPlaces);

// @route   POST /api/cities/:slug/bookmark
// @desc    Bookmark a city
// @access  Private
router.post('/:slug/bookmark', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format')
], bookmarkCity);

// @route   DELETE /api/cities/:slug/bookmark
// @desc    Remove city bookmark
// @access  Private
router.delete('/:slug/bookmark', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format')
], unbookmarkCity);

// @route   GET /api/cities/:slug/stats
// @desc    Get city statistics
// @access  Public
router.get('/:slug/stats', [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format')
], getCityStats);

// @route   GET /api/cities/:slug/reviews
// @desc    Get city reviews
// @access  Public
router.get('/:slug/reviews', [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format')
], getCityReviews);

// @route   POST /api/cities/:slug/reviews
// @desc    Add review to city
// @access  Private
router.post('/:slug/reviews', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format'),
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('comment')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Comment must be between 10 and 1000 characters')
], addCityReview);

// @route   PUT /api/cities/:slug/reviews/:reviewId
// @desc    Update city review
// @access  Private (Review owner only)
router.put('/:slug/reviews/:reviewId', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format'),
  param('reviewId')
    .isMongoId()
    .withMessage('Invalid review ID'),
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('comment')
    .optional()
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Comment must be between 10 and 1000 characters')
], updateCityReview);

// @route   DELETE /api/cities/:slug/reviews/:reviewId
// @desc    Delete city review
// @access  Private (Review owner, Admin, or Moderator)
router.delete('/:slug/reviews/:reviewId', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid city slug format'),
  param('reviewId')
    .isMongoId()
    .withMessage('Invalid review ID')
], deleteCityReview);

export default router;
