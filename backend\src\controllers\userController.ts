import { Response } from 'express';
import { validationResult } from 'express-validator';
import User from '../models/User';
import { Itinerary } from '../models/Itinerary';
import Place from '../models/Place';
import City from '../models/City';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

// @desc    Get user profile
// @route   GET /api/users/:userId
// @access  Public (if profile is public) / Private (own profile)
export const getUserProfile = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const user = await User.findById(req.params.userId)
    .populate('contributedPlaces', 'name slug images averageRating')
    .select('-email -bookmarkedCities -bookmarkedPlaces');

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Check if profile is public or if it's the user's own profile
  const isOwnProfile = req.user && req.user._id.toString() === user._id.toString();
  
  if (!user.preferences.publicProfile && !isOwnProfile) {
    return res.status(403).json({
      success: false,
      error: 'This profile is private'
    });
  }

  res.json({
    success: true,
    data: {
      user
    }
  });
});



// @desc    Get user contributions
// @route   GET /api/users/:userId/contributions
// @access  Public (if profile is public) / Private (own contributions)
export const getUserContributions = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const user = await User.findById(req.params.userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Check if profile is public or if it's the user's own profile
  const isOwnProfile = req.user && req.user._id.toString() === user._id.toString();
  
  if (!user.preferences.publicProfile && !isOwnProfile) {
    return res.status(403).json({
      success: false,
      error: 'This profile is private'
    });
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  const contributions = await User.findById(req.params.userId)
    .populate({
      path: 'contributedPlaces',
      select: 'name slug description images averageRating category isPublished',
      populate: {
        path: 'city',
        select: 'name slug'
      },
      options: {
        skip: (page - 1) * limit,
        limit: limit,
        sort: { createdAt: -1 }
      }
    })
    .select('contributedPlaces');

  res.json({
    success: true,
    data: {
      contributions: contributions?.contributedPlaces || [],
      pagination: {
        page,
        limit
      }
    }
  });
});

// @desc    Update user preferences
// @route   PUT /api/users/:userId/preferences
// @access  Private (own preferences only)
export const updateUserPreferences = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  // Check if user is updating their own preferences
  if (req.user!._id.toString() !== req.params.userId) {
    return res.status(403).json({
      success: false,
      error: 'You can only update your own preferences'
    });
  }

  const allowedPreferences = ['newsletter', 'notifications', 'publicProfile'];
  const updates: any = {};

  Object.keys(req.body).forEach(key => {
    if (allowedPreferences.includes(key)) {
      updates[`preferences.${key}`] = req.body[key];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.params.userId,
    updates,
    { new: true, runValidators: true }
  );

  res.json({
    success: true,
    message: 'Preferences updated successfully',
    data: {
      preferences: user?.preferences
    }
  });
});

// @desc    Get user dashboard data
// @route   GET /api/users/dashboard
// @access  Private
export const getDashboard = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;

  // Get user with populated data
  const user = await User.findById(userId)
    .populate('bookmarkedPlaces', 'name slug category images city averageRating')
    .populate('bookmarkedCities', 'name slug country images averageRating')
    .populate('likedPlaces', 'name slug category images city averageRating')
    .populate('itineraries', 'title duration city status createdAt');

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Get recent itineraries
  const recentItineraries = await Itinerary.find({ user: userId })
    .populate('city', 'name slug country')
    .sort({ updatedAt: -1 })
    .limit(5);

  // Get visited places count
  const visitedPlacesCount = user.visitedPlaces?.length || 0;

  // Get recent activity (last 5 bookmarks/likes)
  const recentBookmarks = await Place.find({
    _id: { $in: user.bookmarkedPlaces.slice(-5) }
  }).populate('city', 'name slug');

  const recentLikes = await Place.find({
    _id: { $in: user.likedPlaces?.slice(-5) || [] }
  }).populate('city', 'name slug');

  // Calculate stats
  const stats = {
    totalBookmarkedPlaces: user.bookmarkedPlaces.length,
    totalBookmarkedCities: user.bookmarkedCities.length,
    totalLikedPlaces: user.likedPlaces?.length || 0,
    totalItineraries: user.itineraries?.length || 0,
    totalVisitedPlaces: visitedPlacesCount
  };

  res.json({
    success: true,
    data: {
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        profile: user.profile,
        preferences: user.preferences
      },
      stats,
      recentItineraries,
      recentBookmarks,
      recentLikes
    }
  });
});

// @desc    Toggle place bookmark
// @route   POST /api/users/bookmark/place/:placeId
// @access  Private
export const togglePlaceBookmark = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { placeId } = req.params;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  const place = await Place.findById(placeId);
  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  const isBookmarked = user.bookmarkedPlaces.includes(placeId as any);

  if (isBookmarked) {
    user.bookmarkedPlaces = user.bookmarkedPlaces.filter(
      id => id.toString() !== placeId
    );
  } else {
    user.bookmarkedPlaces.push(placeId as any);
  }

  await user.save();

  res.json({
    success: true,
    data: {
      isBookmarked: !isBookmarked,
      message: isBookmarked ? 'Place removed from bookmarks' : 'Place bookmarked'
    }
  });
});

// @desc    Toggle place like
// @route   POST /api/users/like/place/:placeId
// @access  Private
export const togglePlaceLike = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { placeId } = req.params;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  const place = await Place.findById(placeId);
  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  // Initialize likedPlaces if it doesn't exist
  if (!user.likedPlaces) {
    user.likedPlaces = [];
  }

  const isLiked = user.likedPlaces.includes(placeId as any);

  if (isLiked) {
    user.likedPlaces = user.likedPlaces.filter(
      id => id.toString() !== placeId
    );
  } else {
    user.likedPlaces.push(placeId as any);
  }

  await user.save();

  res.json({
    success: true,
    data: {
      isLiked: !isLiked,
      message: isLiked ? 'Place unliked' : 'Place liked'
    }
  });
});

// @desc    Get user bookmarks with pagination
// @route   GET /api/users/bookmarks
// @access  Private
export const getUserBookmarks = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { type = 'places', page = 1, limit = 20 } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  let bookmarks;
  let total;

  if (type === 'places') {
    total = user.bookmarkedPlaces.length;
    bookmarks = await Place.find({
      _id: { $in: user.bookmarkedPlaces }
    })
    .populate('city', 'name slug country')
    .skip(skip)
    .limit(limitNum)
    .sort({ createdAt: -1 });
  } else if (type === 'cities') {
    total = user.bookmarkedCities.length;
    bookmarks = await City.find({
      _id: { $in: user.bookmarkedCities }
    })
    .skip(skip)
    .limit(limitNum)
    .sort({ createdAt: -1 });
  } else if (type === 'likes') {
    total = user.likedPlaces?.length || 0;
    bookmarks = await Place.find({
      _id: { $in: user.likedPlaces || [] }
    })
    .populate('city', 'name slug country')
    .skip(skip)
    .limit(limitNum)
    .sort({ createdAt: -1 });
  }

  res.json({
    success: true,
    data: {
      bookmarks,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    }
  });
});

// @desc    Toggle city bookmark
// @route   POST /api/users/bookmark/city/:cityId
// @access  Private
export const toggleCityBookmark = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { cityId } = req.params;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  const city = await City.findById(cityId);
  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  const isBookmarked = user.bookmarkedCities.includes(cityId as any);

  if (isBookmarked) {
    user.bookmarkedCities = user.bookmarkedCities.filter(
      id => id.toString() !== cityId
    );
  } else {
    user.bookmarkedCities.push(cityId as any);
  }

  await user.save();

  res.json({
    success: true,
    data: {
      isBookmarked: !isBookmarked,
      message: isBookmarked ? 'City removed from bookmarks' : 'City bookmarked'
    }
  });
});

// @desc    Get place bookmark/like status
// @route   GET /api/users/place-status/:placeId
// @access  Private
export const getPlaceStatus = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { placeId } = req.params;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  const isBookmarked = user.bookmarkedPlaces.includes(placeId as any);
  const isLiked = user.likedPlaces?.includes(placeId as any) || false;

  res.json({
    success: true,
    data: {
      isBookmarked,
      isLiked
    }
  });
});

// @desc    Get city bookmark status
// @route   GET /api/users/city-status/:cityId
// @access  Private
export const getCityStatus = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { cityId } = req.params;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  const isBookmarked = user.bookmarkedCities.includes(cityId as any);

  res.json({
    success: true,
    data: {
      isBookmarked
    }
  });
});

// @desc    Get user's visited places
// @route   GET /api/users/visited-places
// @access  Private
export const getVisitedPlaces = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const sortBy = req.query.sortBy as string || 'visitDate';
  const sortOrder = req.query.sortOrder as string || 'desc';
  const search = req.query.search as string || '';
  const category = req.query.category as string || '';
  const rating = req.query.rating as string || '';

  try {
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    let visitedPlaces = user.visitedPlaces || [];

    // If there are visited places, populate them
    if (visitedPlaces.length > 0) {
      await user.populate({
        path: 'visitedPlaces.place',
        select: 'name slug category images city averageRating totalReviews description',
        populate: {
          path: 'city',
          select: 'name country'
        }
      });
      visitedPlaces = user.visitedPlaces;
    }

  // Filter by search term
  if (search) {
    visitedPlaces = visitedPlaces.filter((visit: any) =>
      visit.place?.name?.toLowerCase().includes(search.toLowerCase()) ||
      visit.place?.city?.name?.toLowerCase().includes(search.toLowerCase()) ||
      visit.place?.city?.country?.toLowerCase().includes(search.toLowerCase())
    );
  }

  // Filter by category
  if (category) {
    visitedPlaces = visitedPlaces.filter((visit: any) =>
      visit.place?.category === category
    );
  }

  // Filter by rating
  if (rating) {
    const ratingNum = parseInt(rating);
    visitedPlaces = visitedPlaces.filter((visit: any) =>
      visit.rating === ratingNum
    );
  }

  // Sort
  visitedPlaces.sort((a: any, b: any) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'visitDate':
        aValue = new Date(a.visitDate);
        bValue = new Date(b.visitDate);
        break;
      case 'rating':
        aValue = a.rating || 0;
        bValue = b.rating || 0;
        break;
      case 'name':
        aValue = a.place?.name || '';
        bValue = b.place?.name || '';
        break;
      default:
        aValue = new Date(a.visitDate);
        bValue = new Date(b.visitDate);
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const paginatedPlaces = visitedPlaces.slice(startIndex, endIndex);

  // Calculate statistics
  const stats = {
    totalVisited: visitedPlaces.length,
    averageRating: visitedPlaces.length > 0
      ? visitedPlaces.reduce((sum: number, visit: any) => sum + (visit.rating || 0), 0) / visitedPlaces.filter((visit: any) => visit.rating).length
      : 0,
    categoriesVisited: [...new Set(visitedPlaces.map((visit: any) => visit.place?.category).filter(Boolean))].length,
    countriesVisited: [...new Set(visitedPlaces.map((visit: any) => visit.place?.city?.country).filter(Boolean))].length,
    citiesVisited: [...new Set(visitedPlaces.map((visit: any) => visit.place?.city?.name).filter(Boolean))].length
  };

    res.json({
      success: true,
      data: {
        visitedPlaces: paginatedPlaces,
        pagination: {
          page,
          limit,
          total: visitedPlaces.length,
          pages: Math.ceil(visitedPlaces.length / limit)
        },
        stats
      }
    });
  } catch (error) {
    console.error('Error in getVisitedPlaces:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// @desc    Add a place to visited places
// @route   POST /api/users/visited-places
// @access  Private
export const addVisitedPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { placeId, visitDate, rating, review } = req.body;

  // Validate place exists
  const place = await Place.findById(placeId);
  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Check if place is already visited
  const existingVisit = user.visitedPlaces.find(
    (visit: any) => visit.place.toString() === placeId
  );

  if (existingVisit) {
    return res.status(400).json({
      success: false,
      error: 'Place already marked as visited'
    });
  }

  // Add to visited places
  user.visitedPlaces.push({
    place: placeId,
    visitDate: new Date(visitDate),
    rating: rating ? parseInt(rating) : undefined,
    review: review || undefined
  } as any);

  await user.save();

  // Populate the newly added place for response
  await user.populate({
    path: 'visitedPlaces.place',
    select: 'name slug category images city averageRating totalReviews description',
    populate: {
      path: 'city',
      select: 'name country'
    }
  });

  const newVisit = user.visitedPlaces[user.visitedPlaces.length - 1];

  res.status(201).json({
    success: true,
    message: 'Place added to visited places',
    data: {
      visitedPlace: newVisit
    }
  });
});

// @desc    Update a visited place
// @route   PUT /api/users/visited-places/:placeId
// @access  Private
export const updateVisitedPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { placeId } = req.params;
  const { visitDate, rating, review } = req.body;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Find the visited place
  const visitIndex = user.visitedPlaces.findIndex(
    (visit: any) => visit.place.toString() === placeId
  );

  if (visitIndex === -1) {
    return res.status(404).json({
      success: false,
      error: 'Visited place not found'
    });
  }

  // Update the visited place
  if (visitDate) user.visitedPlaces[visitIndex].visitDate = new Date(visitDate);
  if (rating !== undefined) user.visitedPlaces[visitIndex].rating = rating ? parseInt(rating) : undefined;
  if (review !== undefined) user.visitedPlaces[visitIndex].review = review || undefined;

  await user.save();

  // Populate for response
  await user.populate({
    path: 'visitedPlaces.place',
    select: 'name slug category images city averageRating totalReviews description',
    populate: {
      path: 'city',
      select: 'name country'
    }
  });

  res.json({
    success: true,
    message: 'Visited place updated successfully',
    data: {
      visitedPlace: user.visitedPlaces[visitIndex]
    }
  });
});

// @desc    Remove a place from visited places
// @route   DELETE /api/users/visited-places/:placeId
// @access  Private
export const removeVisitedPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { placeId } = req.params;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Find and remove the visited place
  const initialLength = user.visitedPlaces.length;
  user.visitedPlaces = user.visitedPlaces.filter(
    (visit: any) => visit.place.toString() !== placeId
  );

  if (user.visitedPlaces.length === initialLength) {
    return res.status(404).json({
      success: false,
      error: 'Visited place not found'
    });
  }

  await user.save();

  res.json({
    success: true,
    message: 'Place removed from visited places'
  });
});

// @desc    Get visited place status
// @route   GET /api/users/visited-status/:placeId
// @access  Private
export const getVisitedPlaceStatus = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!._id;
  const { placeId } = req.params;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  const visitedPlace = user.visitedPlaces.find(
    (visit: any) => visit.place.toString() === placeId
  );

  res.json({
    success: true,
    data: {
      isVisited: !!visitedPlace,
      visitDetails: visitedPlace || null
    }
  });
});
