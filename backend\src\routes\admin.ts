import express from 'express';
import { body, query, param } from 'express-validator';

// Admin Auth Controllers
import {
  adminLogin,
  getAdminProfile,
  updateAdminProfile,
  changeAdminPassword,
  adminLogout
} from '../controllers/adminAuthController';

// Admin User Management Controllers
import {
  getUsers,
  getUser,
  updateUser,
  deleteUser,
  toggleUserStatus,
  bulkDeleteUsers,
  getUserStats
} from '../controllers/adminUserController';

// Admin Dashboard Controllers
import {
  getDashboardStats,
  getRecentActivity,
  getAnalytics
} from '../controllers/adminDashboardController';

// Admin City Management Controllers
import {
  getCities,
  getCity,
  createCity,
  updateCity,
  deleteCity,
  toggleCityStatus,
  toggleCityFeatured,
  bulkDeleteCities,
  getCityStats
} from '../controllers/adminCityController';

// Admin Place Management Controllers
import {
  getPlaces,
  getPlace,
  createPlace,
  updatePlace,
  deletePlace,
  togglePlaceStatus,
  togglePlaceFeatured,
  bulkDeletePlaces,
  getPlaceStats
} from '../controllers/adminPlaceController';

// Admin Review Management Controllers
import {
  getReviews,
  getReview,
  approveReview,
  rejectReview,
  deleteReview,
  bulkApproveReviews,
  bulkRejectReviews,
  getReviewStats
} from '../controllers/adminReviewController';

// Admin Media Management Controllers
import {
  getMediaFiles,
  uploadMediaFile,
  deleteMediaFile,
  getMediaFolders,
  createMediaFolder,
  getMediaStats
} from '../controllers/adminMediaController';

// Admin Settings Controllers
import {
  getSettings,
  updateSettings,
  getSystemInfo,
  testEmailConfig,
  clearCache,
  exportData,
  getBackupStatus
} from '../controllers/adminSettingsController';

// Admin Middleware
import { protectAdmin, requireAdmin, requireSuperAdmin } from '../middleware/adminAuth';
import { upload, handleUploadError } from '../middleware/upload';

const router = express.Router();

// ============================================================================
// ADMIN AUTHENTICATION ROUTES
// ============================================================================

// @route   POST /api/admin/auth/login
// @desc    Admin login
// @access  Public
router.post('/auth/login', [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
], adminLogin);

// @route   GET /api/admin/auth/me
// @desc    Get current admin profile
// @access  Private (Admin)
router.get('/auth/me', protectAdmin, getAdminProfile);

// @route   PUT /api/admin/auth/profile
// @desc    Update admin profile
// @access  Private (Admin)
router.put('/auth/profile', [
  protectAdmin,
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email')
], updateAdminProfile);

// @route   PUT /api/admin/auth/password
// @desc    Change admin password
// @access  Private (Admin)
router.put('/auth/password', [
  protectAdmin,
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters')
], changeAdminPassword);

// @route   POST /api/admin/auth/logout
// @desc    Admin logout
// @access  Private (Admin)
router.post('/auth/logout', protectAdmin, adminLogout);

// ============================================================================
// ADMIN DASHBOARD ROUTES
// ============================================================================

// @route   GET /api/admin/dashboard/stats
// @desc    Get dashboard statistics
// @access  Private (Admin)
router.get('/dashboard/stats', protectAdmin, getDashboardStats);

// @route   GET /api/admin/dashboard/activity
// @desc    Get recent activity
// @access  Private (Admin)
router.get('/dashboard/activity', [
  protectAdmin,
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getRecentActivity);

// @route   GET /api/admin/dashboard/analytics
// @desc    Get analytics data
// @access  Private (Admin)
router.get('/dashboard/analytics', [
  protectAdmin,
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Days must be between 1 and 365')
], getAnalytics);

// ============================================================================
// ADMIN USER MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/users/stats
// @desc    Get user statistics
// @access  Private (Admin)
router.get('/users/stats', protectAdmin, getUserStats);

// @route   POST /api/admin/users/bulk-delete
// @desc    Bulk delete users
// @access  Private (Super Admin)
router.post('/users/bulk-delete', [
  protectAdmin,
  requireSuperAdmin,
  body('ids')
    .isArray({ min: 1 })
    .withMessage('Please provide an array of user IDs')
], bulkDeleteUsers);

// @route   GET /api/admin/users
// @desc    Get all users with pagination and filtering
// @access  Private (Admin)
router.get('/users', [
  protectAdmin,
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('sortBy')
    .optional()
    .isIn(['name', 'email', 'createdAt', 'lastLoginAt', 'role'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc')
], getUsers);

// @route   GET /api/admin/users/:id
// @desc    Get single user
// @access  Private (Admin)
router.get('/users/:id', [
  protectAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid user ID')
], getUser);

// @route   PUT /api/admin/users/:id
// @desc    Update user
// @access  Private (Admin)
router.put('/users/:id', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid user ID'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email'),
  body('role')
    .optional()
    .isIn(['user', 'contributor', 'moderator'])
    .withMessage('Invalid role'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
], updateUser);

// @route   DELETE /api/admin/users/:id
// @desc    Delete user
// @access  Private (Super Admin)
router.delete('/users/:id', [
  protectAdmin,
  requireSuperAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid user ID')
], deleteUser);

// @route   PATCH /api/admin/users/:id/toggle-status
// @desc    Toggle user active status
// @access  Private (Admin)
router.patch('/users/:id/toggle-status', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid user ID')
], toggleUserStatus);

// ============================================================================
// ADMIN CITY MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/cities/stats
// @desc    Get city statistics
// @access  Private (Admin)
router.get('/cities/stats', protectAdmin, getCityStats);

// @route   POST /api/admin/cities/bulk-delete
// @desc    Bulk delete cities
// @access  Private (Super Admin)
router.post('/cities/bulk-delete', [
  protectAdmin,
  requireSuperAdmin,
  body('ids')
    .isArray({ min: 1 })
    .withMessage('Please provide an array of city IDs')
], bulkDeleteCities);

// @route   GET /api/admin/cities
// @desc    Get all cities with pagination and filtering
// @access  Private (Admin)
router.get('/cities', [
  protectAdmin,
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
], getCities);

// @route   POST /api/admin/cities
// @desc    Create new city
// @access  Private (Admin)
router.post('/cities', [
  protectAdmin,
  requireAdmin,
  body('name')
    .notEmpty()
    .withMessage('City name is required'),
  body('country')
    .notEmpty()
    .withMessage('Country is required'),
  body('description')
    .notEmpty()
    .withMessage('Description is required'),
  body('coordinates.latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Valid latitude is required'),
  body('coordinates.longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Valid longitude is required')
], createCity);

// @route   GET /api/admin/cities/:id
// @desc    Get single city
// @access  Private (Admin)
router.get('/cities/:id', [
  protectAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid city ID')
], getCity);

// @route   PUT /api/admin/cities/:id
// @desc    Update city
// @access  Private (Admin)
router.put('/cities/:id', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid city ID')
], updateCity);

// @route   DELETE /api/admin/cities/:id
// @desc    Delete city
// @access  Private (Super Admin)
router.delete('/cities/:id', [
  protectAdmin,
  requireSuperAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid city ID')
], deleteCity);

// @route   PATCH /api/admin/cities/:id/toggle-status
// @desc    Toggle city published status
// @access  Private (Admin)
router.patch('/cities/:id/toggle-status', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid city ID')
], toggleCityStatus);

// @route   PATCH /api/admin/cities/:id/toggle-featured
// @desc    Toggle city featured status
// @access  Private (Admin)
router.patch('/cities/:id/toggle-featured', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid city ID')
], toggleCityFeatured);

// ============================================================================
// ADMIN PLACE MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/places/stats
// @desc    Get place statistics
// @access  Private (Admin)
router.get('/places/stats', protectAdmin, getPlaceStats);

// @route   POST /api/admin/places/bulk-delete
// @desc    Bulk delete places
// @access  Private (Super Admin)
router.post('/places/bulk-delete', [
  protectAdmin,
  requireSuperAdmin,
  body('ids')
    .isArray({ min: 1 })
    .withMessage('Please provide an array of place IDs')
], bulkDeletePlaces);

// @route   GET /api/admin/places
// @desc    Get all places with pagination and filtering
// @access  Private (Admin)
router.get('/places', [
  protectAdmin,
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
], getPlaces);

// @route   POST /api/admin/places
// @desc    Create new place
// @access  Private (Admin)
router.post('/places', [
  protectAdmin,
  requireAdmin,
  body('name')
    .notEmpty()
    .withMessage('Place name is required'),
  body('description')
    .notEmpty()
    .withMessage('Description is required'),
  body('category')
    .notEmpty()
    .withMessage('Category is required'),
  body('city')
    .isMongoId()
    .withMessage('Valid city ID is required'),
  body('coordinates.latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Valid latitude is required'),
  body('coordinates.longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Valid longitude is required')
], createPlace);

// @route   GET /api/admin/places/:id
// @desc    Get single place
// @access  Private (Admin)
router.get('/places/:id', [
  protectAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid place ID')
], getPlace);

// @route   PUT /api/admin/places/:id
// @desc    Update place
// @access  Private (Admin)
router.put('/places/:id', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid place ID')
], updatePlace);

// @route   DELETE /api/admin/places/:id
// @desc    Delete place
// @access  Private (Super Admin)
router.delete('/places/:id', [
  protectAdmin,
  requireSuperAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid place ID')
], deletePlace);

// @route   PATCH /api/admin/places/:id/toggle-status
// @desc    Toggle place published status
// @access  Private (Admin)
router.patch('/places/:id/toggle-status', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid place ID')
], togglePlaceStatus);

// @route   PATCH /api/admin/places/:id/toggle-featured
// @desc    Toggle place featured status
// @access  Private (Admin)
router.patch('/places/:id/toggle-featured', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid place ID')
], togglePlaceFeatured);

// ============================================================================
// ADMIN REVIEW MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/reviews/stats
// @desc    Get review statistics
// @access  Private (Admin)
router.get('/reviews/stats', protectAdmin, getReviewStats);

// @route   POST /api/admin/reviews/bulk-approve
// @desc    Bulk approve reviews
// @access  Private (Admin)
router.post('/reviews/bulk-approve', [
  protectAdmin,
  requireAdmin,
  body('ids')
    .isArray({ min: 1 })
    .withMessage('Please provide an array of review IDs')
], bulkApproveReviews);

// @route   POST /api/admin/reviews/bulk-reject
// @desc    Bulk reject reviews
// @access  Private (Admin)
router.post('/reviews/bulk-reject', [
  protectAdmin,
  requireAdmin,
  body('ids')
    .isArray({ min: 1 })
    .withMessage('Please provide an array of review IDs')
], bulkRejectReviews);

// @route   GET /api/admin/reviews
// @desc    Get all reviews with pagination and filtering
// @access  Private (Admin)
router.get('/reviews', [
  protectAdmin,
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
], getReviews);

// @route   GET /api/admin/reviews/:id
// @desc    Get single review
// @access  Private (Admin)
router.get('/reviews/:id', [
  protectAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid review ID')
], getReview);

// @route   PATCH /api/admin/reviews/:id/approve
// @desc    Approve review
// @access  Private (Admin)
router.patch('/reviews/:id/approve', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid review ID')
], approveReview);

// @route   PATCH /api/admin/reviews/:id/reject
// @desc    Reject review
// @access  Private (Admin)
router.patch('/reviews/:id/reject', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid review ID')
], rejectReview);

// @route   DELETE /api/admin/reviews/:id
// @desc    Delete review
// @access  Private (Admin)
router.delete('/reviews/:id', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid review ID')
], deleteReview);

// ===== MEDIA MANAGEMENT ROUTES =====

// @route   GET /api/admin/media
// @desc    Get media files
// @access  Private (Admin)
router.get('/media', [
  protectAdmin,
  query('folder').optional().isString(),
  query('type').optional().isIn(['image', 'video', 'document']),
  query('search').optional().isString(),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], getMediaFiles);

// @route   POST /api/admin/media/upload
// @desc    Upload media file
// @access  Private (Admin)
router.post('/media/upload', [
  protectAdmin,
  requireAdmin,
  upload.single('file')
], uploadMediaFile, handleUploadError);

// @route   DELETE /api/admin/media/:filename
// @desc    Delete media file
// @access  Private (Admin)
router.delete('/media/:filename', [
  protectAdmin,
  requireAdmin,
  param('filename').notEmpty().withMessage('Filename is required')
], deleteMediaFile);

// @route   GET /api/admin/media/folders
// @desc    Get media folders
// @access  Private (Admin)
router.get('/media/folders', [
  protectAdmin
], getMediaFolders);

// @route   POST /api/admin/media/folders
// @desc    Create media folder
// @access  Private (Admin)
router.post('/media/folders', [
  protectAdmin,
  requireAdmin,
  body('name').notEmpty().withMessage('Folder name is required')
], createMediaFolder);

// @route   GET /api/admin/media/stats
// @desc    Get media statistics
// @access  Private (Admin)
router.get('/media/stats', [
  protectAdmin
], getMediaStats);

// ===== SETTINGS ROUTES =====

// @route   GET /api/admin/settings
// @desc    Get application settings
// @access  Private (Admin)
router.get('/settings', [
  protectAdmin
], getSettings);

// @route   PUT /api/admin/settings
// @desc    Update application settings
// @access  Private (Super Admin)
router.put('/settings', [
  protectAdmin,
  requireSuperAdmin
], updateSettings);

// @route   GET /api/admin/settings/system
// @desc    Get system information
// @access  Private (Admin)
router.get('/settings/system', [
  protectAdmin
], getSystemInfo);

// @route   POST /api/admin/settings/test-email
// @desc    Test email configuration
// @access  Private (Admin)
router.post('/settings/test-email', [
  protectAdmin,
  body('email').isEmail().withMessage('Valid email is required')
], testEmailConfig);

// @route   POST /api/admin/settings/clear-cache
// @desc    Clear application cache
// @access  Private (Admin)
router.post('/settings/clear-cache', [
  protectAdmin,
  requireAdmin
], clearCache);

// @route   GET /api/admin/settings/export
// @desc    Export application data
// @access  Private (Super Admin)
router.get('/settings/export', [
  protectAdmin,
  requireSuperAdmin
], exportData);

// @route   GET /api/admin/settings/backup-status
// @desc    Get backup status
// @access  Private (Admin)
router.get('/settings/backup-status', [
  protectAdmin
], getBackupStatus);

export default router;
