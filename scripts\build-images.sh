#!/bin/bash

# HeritEdge Docker Images Build Script
echo "🚀 Building HeritEdge Docker Images..."

# Set error handling
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running ✓"

# Build backend image
print_status "Building backend image..."
cd backend
if docker build -t heritedge/backend:latest .; then
    print_success "Backend image built successfully"
else
    print_error "Failed to build backend image"
    exit 1
fi
cd ..

# Build frontend image
print_status "Building frontend image..."
cd frontend
if docker build -t heritedge/frontend:latest .; then
    print_success "Frontend image built successfully"
else
    print_error "Failed to build frontend image"
    exit 1
fi
cd ..

# Build admin image
print_status "Building admin image..."
cd admin
if docker build -t heritedge/admin:latest .; then
    print_success "Admin image built successfully"
else
    print_error "Failed to build admin image"
    exit 1
fi
cd ..

# List built images
print_status "Built images:"
docker images | grep heritedge

print_success "All images built successfully! 🎉"
print_status "You can now deploy to Kubernetes using: ./scripts/deploy-k8s.sh"
