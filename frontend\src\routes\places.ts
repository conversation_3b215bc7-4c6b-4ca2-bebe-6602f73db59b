import express from 'express';
import { body, query, param } from 'express-validator';
import {
  getPlaces,
  getPlace,
  createPlace,
  updatePlace,
  deletePlace,
  addReview,
  updateReview,
  deleteReview,
  bookmarkPlace,
  unbookmarkPlace,
  getNearbyPlaces
} from '../controllers/placeController';
import { protect, authorize, optionalAuth } from '../middleware/auth';

const router = express.Router();

// @route   GET /api/places
// @desc    Get all places with filtering, sorting, and pagination
// @access  Public
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('city')
    .optional()
    .isMongoId()
    .withMessage('Invalid city ID'),
  query('category')
    .optional()
    .isIn(['historical', 'attraction', 'restaurant', 'hotel', 'shopping', 'entertainment', 'nature', 'religious', 'museum', 'other'])
    .withMessage('Invalid category'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Search term must be at least 2 characters'),
  query('sort')
    .optional()
    .isIn(['name', '-name', 'averageRating', '-averageRating', 'popularityScore', '-popularityScore', 'createdAt', '-createdAt'])
    .withMessage('Invalid sort parameter'),
  query('minRating')
    .optional()
    .isFloat({ min: 0, max: 5 })
    .withMessage('Minimum rating must be between 0 and 5'),
  query('priceRange')
    .optional()
    .isIn(['budget', 'moderate', 'expensive', 'luxury'])
    .withMessage('Invalid price range')
], getPlaces);

// @route   GET /api/places/nearby
// @desc    Get nearby places
// @access  Public
router.get('/nearby', [
  query('latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  query('longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  query('radius')
    .optional()
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Radius must be between 0.1 and 100 km'),
  query('category')
    .optional()
    .isIn(['historical', 'attraction', 'restaurant', 'hotel', 'shopping', 'entertainment', 'nature', 'religious', 'museum', 'other'])
    .withMessage('Invalid category'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getNearbyPlaces);

// @route   GET /api/places/:slug
// @desc    Get single place by slug
// @access  Public
router.get('/:slug', [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid place slug format')
], optionalAuth, getPlace);

// @route   POST /api/places
// @desc    Create new place
// @access  Private
router.post('/', protect, [
  body('name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Place name must be between 2 and 200 characters'),
  body('city')
    .isMongoId()
    .withMessage('Valid city ID is required'),
  body('category')
    .isIn(['historical', 'attraction', 'restaurant', 'hotel', 'shopping', 'entertainment', 'nature', 'religious', 'museum', 'other'])
    .withMessage('Valid category is required'),
  body('description')
    .trim()
    .isLength({ min: 50, max: 3000 })
    .withMessage('Description must be between 50 and 3000 characters'),
  body('coordinates.latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('coordinates.longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  body('address.area')
    .trim()
    .notEmpty()
    .withMessage('Area is required'),
  body('address.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  body('address.country')
    .trim()
    .notEmpty()
    .withMessage('Country is required'),
  body('seoMetadata.title')
    .trim()
    .isLength({ min: 10, max: 60 })
    .withMessage('SEO title must be between 10 and 60 characters'),
  body('seoMetadata.description')
    .trim()
    .isLength({ min: 50, max: 160 })
    .withMessage('SEO description must be between 50 and 160 characters')
], createPlace);

// @route   PUT /api/places/:slug
// @desc    Update place
// @access  Private (Owner, Admin, or Moderator)
router.put('/:slug', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid place slug format'),
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Place name must be between 2 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 50, max: 3000 })
    .withMessage('Description must be between 50 and 3000 characters'),
  body('coordinates.latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('coordinates.longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180')
], updatePlace);

// @route   DELETE /api/places/:slug
// @desc    Delete place
// @access  Private (Owner, Admin, or Moderator)
router.delete('/:slug', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid place slug format')
], deletePlace);

// @route   POST /api/places/:slug/reviews
// @desc    Add review to place
// @access  Private
router.post('/:slug/reviews', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid place slug format'),
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('comment')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Comment must be between 10 and 1000 characters')
], addReview);

// @route   PUT /api/places/:slug/reviews/:reviewId
// @desc    Update review
// @access  Private (Review owner only)
router.put('/:slug/reviews/:reviewId', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid place slug format'),
  param('reviewId')
    .isMongoId()
    .withMessage('Invalid review ID'),
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('comment')
    .optional()
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Comment must be between 10 and 1000 characters')
], updateReview);

// @route   DELETE /api/places/:slug/reviews/:reviewId
// @desc    Delete review
// @access  Private (Review owner, Admin, or Moderator)
router.delete('/:slug/reviews/:reviewId', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid place slug format'),
  param('reviewId')
    .isMongoId()
    .withMessage('Invalid review ID')
], deleteReview);

// @route   POST /api/places/:slug/bookmark
// @desc    Bookmark a place
// @access  Private
router.post('/:slug/bookmark', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid place slug format')
], bookmarkPlace);

// @route   DELETE /api/places/:slug/bookmark
// @desc    Remove place bookmark
// @access  Private
router.delete('/:slug/bookmark', protect, [
  param('slug')
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('Invalid place slug format')
], unbookmarkPlace);

export default router;
