const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge';

// Simple city schema for testing
const citySchema = new mongoose.Schema({
  name: String,
  slug: String,
  country: String,
  description: String,
  overview: String,
  images: [{
    url: String,
    alt: String,
    caption: String,
    isPrimary: Boolean
  }],
  coordinates: {
    latitude: Number,
    longitude: Number
  },
  population: Number,
  area: Number,
  timezone: String,
  currency: String,
  languages: [String],
  climate: {
    type: String,
    bestTimeToVisit: String,
    averageTemperature: {
      summer: Number,
      winter: Number
    }
  },
  transportation: {
    howToReach: {
      byAir: String,
      byRoad: String,
      byRail: String
    },
    localTransport: [String],
    airports: [{
      name: String,
      code: String,
      distance: Number
    }]
  },
  economy: {
    majorIndustries: [String],
    gdp: Number
  },
  culture: {
    festivals: [String],
    traditions: [String],
    artAndCrafts: [String]
  },
  places: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Place' }],
  averageRating: { type: Number, default: 4.5 },
  totalReviews: { type: Number, default: 1000 },
  isPublished: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: true },
  tags: [String],
  seoMetadata: {
    title: String,
    description: String,
    keywords: [String]
  }
}, {
  timestamps: true
});

const City = mongoose.model('City', citySchema);

const testCity = {
  name: 'Paris',
  slug: 'paris',
  country: 'France',
  description: 'The City of Light, known for its art, fashion, gastronomy, and culture.',
  overview: 'Paris, the capital of France, is a major European city and a global center for art, fashion, gastronomy and culture.',
  images: [
    {
      url: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=800&h=600&fit=crop',
      alt: 'Paris skyline with Eiffel Tower',
      caption: 'The iconic Eiffel Tower dominates the Paris skyline',
      isPrimary: true
    }
  ],
  coordinates: {
    latitude: 48.8566,
    longitude: 2.3522
  },
  population: 2161000,
  area: 105.4,
  timezone: 'Europe/Paris',
  currency: 'EUR',
  languages: ['French'],
  climate: {
    type: 'Oceanic',
    bestTimeToVisit: 'April to June, September to October',
    averageTemperature: {
      summer: 25,
      winter: 7
    }
  },
  transportation: {
    howToReach: {
      byAir: 'Charles de Gaulle Airport (CDG) and Orly Airport (ORY) serve the city.',
      byRoad: 'Well connected by highways from all major European cities.',
      byRail: 'Gare du Nord and other major stations connect Paris to European cities.'
    },
    localTransport: ['Metro', 'Bus', 'Tram', 'Taxi'],
    airports: [
      { name: 'Charles de Gaulle', code: 'CDG', distance: 25 },
      { name: 'Orly', code: 'ORY', distance: 13 }
    ]
  },
  economy: {
    majorIndustries: ['Tourism', 'Fashion', 'Technology', 'Finance'],
    gdp: 739000000000
  },
  culture: {
    festivals: ['Fête de la Musique', 'Nuit Blanche', 'Paris Fashion Week'],
    traditions: ['Café culture', 'Sunday markets', 'Evening strolls'],
    artAndCrafts: ['Fashion design', 'Perfumery', 'Culinary arts']
  },
  averageRating: 4.8,
  totalReviews: 15420,
  isPublished: true,
  isFeatured: true,
  tags: ['romantic', 'art', 'fashion', 'cuisine', 'historic'],
  seoMetadata: {
    title: 'Paris - The City of Light | HeritEdge',
    description: 'Discover Paris, the romantic capital of France.',
    keywords: ['Paris', 'France', 'Eiffel Tower', 'travel']
  }
};

async function addTestCity() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    // Clear existing cities
    console.log('🗑️ Clearing existing cities...');
    await City.deleteMany({});
    console.log('✅ Cleared existing cities');

    // Add test city
    console.log('📍 Adding test city...');
    const city = await City.create(testCity);
    console.log(`✅ Successfully added: ${city.name}`);

    // Verify
    const totalCities = await City.countDocuments();
    console.log(`📊 Total cities in database: ${totalCities}`);

    console.log('🎉 Test city added successfully!');
    console.log('🌐 You can now view cities at: http://localhost:3001/cities');

  } catch (error) {
    console.error('❌ Failed to add test city:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  addTestCity();
}

module.exports = { addTestCity };
