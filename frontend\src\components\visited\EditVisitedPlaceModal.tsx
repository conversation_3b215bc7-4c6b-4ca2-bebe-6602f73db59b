'use client';

import { useState } from 'react';
import { apiClient } from '@/lib/api';
import { VisitedPlace } from '@/types';
import { StarRating } from '@/components/ui/StarRating';
import { 
  XMarkIcon, 
  MapPinIcon,
  CalendarIcon,
  StarIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';

interface EditVisitedPlaceModalProps {
  visitedPlace: VisitedPlace;
  onClose: () => void;
  onUpdate: () => void;
}

export function EditVisitedPlaceModal({ visitedPlace, onClose, onUpdate }: EditVisitedPlaceModalProps) {
  const [submitting, setSubmitting] = useState(false);
  
  // Form data
  const [visitDate, setVisitDate] = useState(
    new Date(visitedPlace.visitDate).toISOString().split('T')[0]
  );
  const [rating, setRating] = useState<number>(visitedPlace.rating || 0);
  const [review, setReview] = useState(visitedPlace.review || '');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSubmitting(true);
      const response = await apiClient.updateVisitedPlace(visitedPlace.place.id, {
        visitDate,
        rating: rating > 0 ? rating : undefined,
        review: review.trim() || undefined
      });

      if (response.success) {
        onUpdate();
      } else {
        alert(response.error || 'Failed to update place');
      }
    } catch (error: any) {
      console.error('Error updating visited place:', error);
      alert(error.message || 'Failed to update place');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Edit Visit Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          <form onSubmit={handleSubmit} className="p-6">
            {/* Place Info */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900">{visitedPlace.place.name}</h3>
              <div className="flex items-center text-sm text-gray-600 mt-1">
                <MapPinIcon className="h-4 w-4 mr-1" />
                <span>
                  {typeof visitedPlace.place.city === 'object' && visitedPlace.place.city !== null
                    ? `${visitedPlace.place.city.name}, ${visitedPlace.place.city.country}`
                    : visitedPlace.place.city || ''}
                </span>
              </div>
              <span className="inline-block mt-2 px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded">
                {visitedPlace.place.category}
              </span>
            </div>

            {/* Visit Date */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <CalendarIcon className="h-4 w-4 inline mr-1" />
                Visit Date
              </label>
              <input
                type="date"
                value={visitDate}
                onChange={(e) => setVisitDate(e.target.value)}
                max={new Date().toISOString().split('T')[0]}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* Rating */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <StarIcon className="h-4 w-4 inline mr-1" />
                Your Rating
              </label>
              <div className="flex items-center space-x-2">
                <StarRating
                  rating={rating}
                  onRatingChange={setRating}
                  size="lg"
                />
                {rating > 0 && (
                  <span className="text-sm text-gray-600">({rating}/5)</span>
                )}
                {rating > 0 && (
                  <button
                    type="button"
                    onClick={() => setRating(0)}
                    className="text-xs text-gray-500 hover:text-gray-700 ml-2"
                  >
                    Clear rating
                  </button>
                )}
              </div>
            </div>

            {/* Review */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <ChatBubbleLeftIcon className="h-4 w-4 inline mr-1" />
                Your Review
              </label>
              <textarea
                value={review}
                onChange={(e) => setReview(e.target.value)}
                placeholder="Share your experience at this place..."
                rows={4}
                maxLength={1000}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
              <div className="text-right text-xs text-gray-500 mt-1">
                {review.length}/1000 characters
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={submitting}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? 'Updating...' : 'Update Visit'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
