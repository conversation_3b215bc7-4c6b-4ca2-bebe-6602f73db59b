import mongoose, { Document, Schema } from 'mongoose';

export interface IVideo extends Document {
  _id: mongoose.Types.ObjectId;
  title: string;
  description?: string;
  youtubeUrl: string;
  youtubeVideoId: string;
  thumbnailUrl?: string;
  duration?: string;
  place: mongoose.Types.ObjectId;
  submittedBy: mongoose.Types.ObjectId;
  submissionDate: Date;
  status: 'pending' | 'approved' | 'rejected';
  reviewedBy?: mongoose.Types.ObjectId;
  reviewDate?: Date;
  reviewNotes?: string;
  tags: string[];
  viewCount: number;
  likes: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const videoSchema = new Schema<IVideo>({
  title: {
    type: String,
    required: [true, 'Video title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  youtubeUrl: {
    type: String,
    required: [true, 'YouTube URL is required'],
    validate: {
      validator: function(url: string) {
        // Validate YouTube URL format
        const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
        return youtubeRegex.test(url);
      },
      message: 'Please provide a valid YouTube URL'
    }
  },
  youtubeVideoId: {
    type: String,
    required: true,
    validate: {
      validator: function(id: string) {
        // YouTube video ID is 11 characters long
        return /^[a-zA-Z0-9_-]{11}$/.test(id);
      },
      message: 'Invalid YouTube video ID'
    }
  },
  thumbnailUrl: {
    type: String,
    default: function() {
      return `https://img.youtube.com/vi/${this.youtubeVideoId}/maxresdefault.jpg`;
    }
  },
  duration: {
    type: String,
    trim: true
  },
  place: {
    type: Schema.Types.ObjectId,
    ref: 'Place',
    required: [true, 'Place reference is required']
  },
  submittedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Submitter reference is required']
  },
  submissionDate: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewDate: {
    type: Date
  },
  reviewNotes: {
    type: String,
    trim: true,
    maxlength: [500, 'Review notes cannot exceed 500 characters']
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  viewCount: {
    type: Number,
    default: 0,
    min: 0
  },
  likes: {
    type: Number,
    default: 0,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
videoSchema.index({ place: 1, status: 1 });
videoSchema.index({ submittedBy: 1 });
videoSchema.index({ status: 1, submissionDate: -1 });
videoSchema.index({ youtubeVideoId: 1 }, { unique: true });
videoSchema.index({ place: 1, youtubeVideoId: 1 }, { unique: true });

// Pre-save middleware to extract YouTube video ID
videoSchema.pre('save', function(next) {
  if (this.isModified('youtubeUrl')) {
    const match = this.youtubeUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
    if (match) {
      this.youtubeVideoId = match[1];
      this.thumbnailUrl = `https://img.youtube.com/vi/${this.youtubeVideoId}/maxresdefault.jpg`;
    }
  }
  next();
});

// Virtual for embedded YouTube URL
videoSchema.virtual('embedUrl').get(function() {
  return `https://www.youtube.com/embed/${this.youtubeVideoId}`;
});

export default mongoose.model<IVideo>('Video', videoSchema);
