#!/bin/bash

# Setup script for GitHub Actions CI/CD
# This script helps configure the CI/CD pipeline for Docker Hub integration

set -e

echo "🚀 Setting up GitHub Actions CI/CD for Travelogix"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "This is not a git repository. Please run this script from the root of your git repository."
    exit 1
fi

print_status "Git repository detected"

# Check if GitHub Actions workflows exist
if [ -d ".github/workflows" ]; then
    print_status "GitHub Actions workflows directory exists"
    
    if [ -f ".github/workflows/docker-build-push.yml" ]; then
        print_status "Docker build workflow found"
    else
        print_error "Docker build workflow not found"
        exit 1
    fi
else
    print_error ".github/workflows directory not found"
    exit 1
fi

echo ""
echo "📋 Setup Checklist"
echo "=================="

echo ""
print_info "1. Docker Hub Account Setup"
echo "   - Create account at https://hub.docker.com if you don't have one"
echo "   - Generate an access token:"
echo "     • Go to Account Settings → Security"
echo "     • Click 'New Access Token'"
echo "     • Name it 'GitHub Actions Travelogix'"
echo "     • Copy the generated token"

echo ""
print_info "2. GitHub Repository Secrets"
echo "   Go to your repository → Settings → Secrets and variables → Actions"
echo "   Add these secrets:"
echo "   • DOCKER_HUB_USERNAME: Your Docker Hub username"
echo "   • DOCKER_HUB_TOKEN: The access token from step 1"

echo ""
print_info "3. Update Docker Hub Username"
read -p "Enter your Docker Hub username: " DOCKER_USERNAME

if [ -n "$DOCKER_USERNAME" ]; then
    # Update the README with the actual username
    if [ -f ".github/README.md" ]; then
        sed -i.bak "s/your-username/$DOCKER_USERNAME/g" .github/README.md
        print_status "Updated .github/README.md with your Docker Hub username"
    fi
    
    # Update docker-compose.yml if it exists
    if [ -f "docker-compose.yml" ]; then
        print_warning "Consider updating docker-compose.yml to use your Docker Hub images:"
        echo "   Replace image names with: $DOCKER_USERNAME/travelogix-[service]:latest"
    fi
    
    # Update Kubernetes deployments
    if [ -d "k8s" ]; then
        print_warning "Consider updating Kubernetes deployment files in k8s/ directory"
        echo "   Replace image names with: $DOCKER_USERNAME/travelogix-[service]:latest"
    fi
else
    print_warning "No Docker Hub username provided. You'll need to update configurations manually."
fi

echo ""
print_info "4. Test the Setup"
echo "   • Push changes to your repository"
echo "   • Check the Actions tab for workflow execution"
echo "   • Verify images are pushed to Docker Hub"

echo ""
print_info "5. Generated Docker Images"
echo "   After successful workflow execution, you'll have:"
echo "   • $DOCKER_USERNAME/travelogix-admin:latest"
echo "   • $DOCKER_USERNAME/travelogix-backend:latest"
echo "   • $DOCKER_USERNAME/travelogix-frontend:latest"

echo ""
print_status "Setup script completed!"
print_info "Next steps:"
echo "1. Commit and push the .github directory to your repository"
echo "2. Configure the GitHub repository secrets as described above"
echo "3. Push a commit to trigger the first workflow run"

echo ""
print_info "Useful commands:"
echo "• View workflow status: Check the Actions tab in your GitHub repository"
echo "• Manual trigger: Go to Actions → Build and Push Docker Images → Run workflow"
echo "• Local testing: Use the scripts in the scripts/ directory"

echo ""
echo "🎉 Happy coding!"
