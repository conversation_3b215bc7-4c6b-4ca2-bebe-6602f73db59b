// MongoDB initialization script for HeritEdge
print('Starting HeritEdge database initialization...');

// Switch to the heritedge database
db = db.getSiblingDB('heritedge');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'name'],
      properties: {
        email: {
          bsonType: 'string',
          description: 'Email must be a string and is required'
        },
        name: {
          bsonType: 'string',
          description: 'Name must be a string and is required'
        }
      }
    }
  }
});

db.createCollection('admins', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'name', 'password'],
      properties: {
        email: {
          bsonType: 'string',
          description: 'Email must be a string and is required'
        },
        name: {
          bsonType: 'string',
          description: 'Name must be a string and is required'
        },
        password: {
          bsonType: 'string',
          description: 'Password must be a string and is required'
        }
      }
    }
  }
});

db.createCollection('cities');
db.createCollection('places');
db.createCollection('reviews');
db.createCollection('itineraries');

// Create indexes for better performance
print('Creating indexes...');

// Users indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ googleId: 1 }, { sparse: true });
db.users.createIndex({ createdAt: 1 });

// Admins indexes
db.admins.createIndex({ email: 1 }, { unique: true });
db.admins.createIndex({ role: 1 });

// Cities indexes
db.cities.createIndex({ name: 1 });
db.cities.createIndex({ slug: 1 }, { unique: true });
db.cities.createIndex({ country: 1 });
db.cities.createIndex({ isActive: 1 });

// Places indexes
db.places.createIndex({ name: 1 });
db.places.createIndex({ slug: 1 }, { unique: true });
db.places.createIndex({ city: 1 });
db.places.createIndex({ category: 1 });
db.places.createIndex({ isActive: 1 });
db.places.createIndex({ location: '2dsphere' });

// Reviews indexes
db.reviews.createIndex({ entityType: 1, entityId: 1 });
db.reviews.createIndex({ user: 1 });
db.reviews.createIndex({ entityType: 1, entityId: 1, user: 1 }, { unique: true });
db.reviews.createIndex({ isApproved: 1 });
db.reviews.createIndex({ isReported: 1 });
db.reviews.createIndex({ createdAt: 1 });

// Itineraries indexes
db.itineraries.createIndex({ user: 1 });
db.itineraries.createIndex({ city: 1 });
db.itineraries.createIndex({ isPublic: 1 });
db.itineraries.createIndex({ createdAt: 1 });

// Create default admin user (password: admin123456)
print('Creating default admin user...');
db.admins.insertOne({
  name: 'System Administrator',
  email: '<EMAIL>',
  password: '$2b$10$8K1p/a0dclxKoNqIfrHb2eIiGQExd6yvAoK8WMnbp7f2kJHNe8ppe', // admin123456
  role: 'super_admin',
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

// Insert sample data for development
print('Inserting sample data...');

// Sample cities
const cities = [
  {
    name: 'Paris',
    slug: 'paris',
    country: 'France',
    description: 'The City of Light, known for its art, fashion, and culture.',
    image: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Tokyo',
    slug: 'tokyo',
    country: 'Japan',
    description: 'A bustling metropolis blending traditional and modern culture.',
    image: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'New York',
    slug: 'new-york',
    country: 'United States',
    description: 'The Big Apple, a global hub for finance, arts, and culture.',
    image: 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const insertedCities = db.cities.insertMany(cities);
const cityIds = Object.values(insertedCities.insertedIds);

// Sample places
const places = [
  {
    name: 'Eiffel Tower',
    slug: 'eiffel-tower',
    city: cityIds[0],
    category: 'landmark',
    description: 'Iconic iron lattice tower and symbol of Paris.',
    image: 'https://images.unsplash.com/photo-1511739001486-6bfe10ce785f',
    location: {
      type: 'Point',
      coordinates: [2.2945, 48.8584]
    },
    address: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Senso-ji Temple',
    slug: 'senso-ji-temple',
    city: cityIds[1],
    category: 'religious',
    description: 'Ancient Buddhist temple in Asakusa district.',
    image: 'https://images.unsplash.com/photo-1545569341-9eb8b30979d9',
    location: {
      type: 'Point',
      coordinates: [139.7967, 35.7148]
    },
    address: '2 Chome-3-1 Asakusa, Taito City, Tokyo 111-0032, Japan',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Central Park',
    slug: 'central-park',
    city: cityIds[2],
    category: 'park',
    description: 'Large public park in Manhattan, perfect for recreation.',
    image: 'https://images.unsplash.com/photo-1564564321837-a57b7070ac4f',
    location: {
      type: 'Point',
      coordinates: [-73.9654, 40.7829]
    },
    address: 'New York, NY 10024, United States',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

db.places.insertMany(places);

print('HeritEdge database initialization completed successfully!');
print('Default admin credentials:');
print('Email: <EMAIL>');
print('Password: admin123456');
print('Please change the default password after first login.');
