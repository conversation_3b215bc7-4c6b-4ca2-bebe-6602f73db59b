const fs = require('fs');
const path = require('path');

// Files and their specific fixes
const fixes = [
  // Itineraries new page
  {
    file: 'src/app/itineraries/new/page.tsx',
    replacements: [
      { from: "Let's create your perfect itinerary", to: "Let&apos;s create your perfect itinerary" }
    ]
  },

  // Itineraries edit page
  {
    file: 'src/app/itineraries/[id]/edit/page.tsx',
    replacements: [
      { from: '"Edit Itinerary"', to: '&quot;Edit Itinerary&quot;' }
    ]
  },

  // Itineraries view page
  {
    file: 'src/app/itineraries/[id]/page.tsx',
    replacements: [
      { from: "Traveler's Notes", to: "Traveler&apos;s Notes" }
    ]
  },

  // Home page
  {
    file: 'src/app/page.tsx',
    replacements: [
      { from: "Discover the world's most beautiful destinations", to: "Discover the world&apos;s most beautiful destinations" }
    ]
  },

  // Visited page
  {
    file: 'src/app/visited/page.tsx',
    replacements: [
      { from: "You haven't visited any places yet.", to: "You haven&apos;t visited any places yet." },
      { from: "You haven't added any places to your visited list yet.", to: "You haven&apos;t added any places to your visited list yet." }
    ]
  },

  // SearchBar component
  {
    file: 'src/components/ui/SearchBar.tsx',
    replacements: [
      { from: 'placeholder="Search cities, places..."', to: 'placeholder=&quot;Search cities, places...&quot;' },
      { from: 'placeholder="Search places..."', to: 'placeholder=&quot;Search places...&quot;' },
      { from: '"', to: '&quot;' }
    ]
  },

  // AddVisitedPlaceModal component
  {
    file: 'src/components/visited/AddVisitedPlaceModal.tsx',
    replacements: [
      { from: 'placeholder="Search for a place..."', to: 'placeholder=&quot;Search for a place...&quot;' }
    ]
  }
];

// Function to apply fixes
function applyFixes() {
  fixes.forEach(({ file, replacements }) => {
    const filePath = path.join(__dirname, file);
    
    if (!fs.existsSync(filePath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    replacements.forEach(({ from, to }) => {
      if (content.includes(from)) {
        content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
        modified = true;
        console.log(`Fixed in ${file}: ${from} -> ${to}`);
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Updated: ${file}`);
    }
  });
}

console.log('Fixing lint errors...');
applyFixes();
console.log('Done!');
