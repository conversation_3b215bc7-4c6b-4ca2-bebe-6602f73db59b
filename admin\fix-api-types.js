const fs = require('fs');

// Read the API file
const filePath = 'src/lib/api.ts';
let content = fs.readFileSync(filePath, 'utf8');

// Replace all 'any' types with more specific types
const replacements = [
  { from: ': any', to: ': unknown' },
  { from: 'any>', to: 'unknown>' },
  { from: 'any[]', to: 'unknown[]' },
  { from: 'any,', to: 'unknown,' },
  { from: 'any)', to: 'unknown)' }
];

let modified = false;
replacements.forEach(({ from, to }) => {
  if (content.includes(from)) {
    content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
    modified = true;
    console.log(`Fixed: ${from} -> ${to}`);
  }
});

if (modified) {
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('Updated API file with proper types');
}

console.log('API types fixed!');
