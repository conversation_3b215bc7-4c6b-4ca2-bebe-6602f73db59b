const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge';

async function importPlacesWithFixedImages() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    // Get collections
    const citiesCollection = mongoose.connection.db.collection('cities');
    const placesCollection = mongoose.connection.db.collection('places');

    // Find Paris city
    const parisCity = await citiesCollection.findOne({ slug: 'paris' });
    if (!parisCity) {
      console.log('❌ Paris city not found. Please make sure Paris city exists in the database.');
      return;
    }

    console.log(`✅ Found Paris city: ${parisCity.name} (ID: ${parisCity._id})`);

    // Load places data with fixed images
    const placesData = require('./places-with-images.json');
    
    // Clear existing places for Paris
    await placesCollection.deleteMany({ city: parisCity._id });
    console.log('🗑️ Cleared existing places for Paris');

    // Update places data with correct city ObjectId
    const updatedPlaces = placesData.map(place => ({
      ...place,
      city: parisCity._id,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    // Insert places
    const result = await placesCollection.insertMany(updatedPlaces);
    console.log(`✅ Successfully imported ${result.insertedCount} places with fixed images`);

    // Verify and display results
    const totalPlaces = await placesCollection.countDocuments({ city: parisCity._id });
    console.log(`📊 Total places for Paris: ${totalPlaces}`);

    // List all places with their image URLs
    const places = await placesCollection.find({ city: parisCity._id }).toArray();
    console.log('\n📍 Places imported with images:');
    places.forEach(place => {
      console.log(`\n🏷️  ${place.name} (${place.category})`);
      if (place.images && place.images.length > 0) {
        console.log(`   📸 Image: ${place.images[0].url.substring(0, 80)}...`);
      } else {
        console.log(`   ❌ No images found`);
      }
    });

    console.log('\n🎉 Places with fixed images imported successfully!');
    console.log('🌐 You can now view places at: http://localhost:3001/cities/paris');
    console.log('📱 Click the "Places" tab to see the places with working images');

  } catch (error) {
    console.error('❌ Failed to import places:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  importPlacesWithFixedImages();
}

module.exports = { importPlacesWithFixedImages };
