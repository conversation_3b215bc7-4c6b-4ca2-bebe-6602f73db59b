import { Response } from 'express';
import { validationResult } from 'express-validator';
import City from '../models/City';
import Place from '../models/Place';
import { Itinerary } from '../models/Itinerary';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

// @desc    Global search across cities and places
// @route   GET /api/search
// @access  Public
export const globalSearch = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { q, type = 'all', page = 1, limit = 20, country, category } = req.query;
  const searchQuery = q as string;
  const searchType = type as string;
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  let results: any = {
    cities: [],
    places: [],
    total: 0
  };

  // Build base query for text search
  const baseQuery: any = {
    $text: { $search: searchQuery },
    isPublished: true
  };

  if (country) {
    baseQuery.country = new RegExp(country as string, 'i');
  }

  // Search cities
  if (searchType === 'all' || searchType === 'cities') {
    const cityQuery = { ...baseQuery };
    
    const cities = await City.find(cityQuery)
      .select('name slug description images averageRating country')
      .sort({ score: { $meta: 'textScore' }, averageRating: -1 })
      .skip(searchType === 'cities' ? skip : 0)
      .limit(searchType === 'cities' ? limitNum : Math.floor(limitNum / 2));

    results.cities = cities;
  }

  // Search places
  if (searchType === 'all' || searchType === 'places') {
    const placeQuery = { ...baseQuery };
    
    if (category) {
      placeQuery.category = category;
    }

    const places = await Place.find(placeQuery)
      .populate('city', 'name slug')
      .select('name slug description images averageRating category')
      .sort({ score: { $meta: 'textScore' }, averageRating: -1 })
      .skip(searchType === 'places' ? skip : 0)
      .limit(searchType === 'places' ? limitNum : Math.floor(limitNum / 2));

    results.places = places;
  }

  // Calculate total results
  if (searchType === 'all') {
    const cityCount = await City.countDocuments(baseQuery);
    const placeCount = await Place.countDocuments({
      ...baseQuery,
      ...(category && { category })
    });
    results.total = cityCount + placeCount;
  } else if (searchType === 'cities') {
    results.total = await City.countDocuments(baseQuery);
  } else if (searchType === 'places') {
    results.total = await Place.countDocuments({
      ...baseQuery,
      ...(category && { category })
    });
  }

  res.json({
    success: true,
    data: {
      query: searchQuery,
      type: searchType,
      results,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: results.total,
        pages: Math.ceil(results.total / limitNum)
      }
    }
  });
});

// @desc    Search cities specifically
// @route   GET /api/search/cities
// @access  Public
export const searchCities = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { q, page = 1, limit = 20, country, minRating } = req.query;
  const searchQuery = q as string;
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const query: any = {
    $text: { $search: searchQuery },
    isPublished: true
  };

  if (country) {
    query.country = new RegExp(country as string, 'i');
  }

  if (minRating) {
    query.averageRating = { $gte: parseFloat(minRating as string) };
  }

  const cities = await City.find(query)
    .select('name slug description images averageRating country')
    .sort({ score: { $meta: 'textScore' }, averageRating: -1 })
    .skip(skip)
    .limit(limitNum);

  const total = await City.countDocuments(query);

  res.json({
    success: true,
    data: {
      query: searchQuery,
      cities,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    }
  });
});

// @desc    Search places specifically
// @route   GET /api/search/places
// @access  Public
export const searchPlaces = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { q, page = 1, limit = 20, city, category, minRating, priceRange } = req.query;
  const searchQuery = q as string;
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const query: any = {
    $text: { $search: searchQuery },
    isPublished: true
  };

  if (city) {
    query.city = city;
  }

  if (category) {
    query.category = category;
  }

  if (minRating) {
    query.averageRating = { $gte: parseFloat(minRating as string) };
  }

  if (priceRange) {
    query['pricing.priceRange'] = priceRange;
  }

  const places = await Place.find(query)
    .populate('city', 'name slug')
    .select('name slug description images averageRating category pricing')
    .sort({ score: { $meta: 'textScore' }, averageRating: -1 })
    .skip(skip)
    .limit(limitNum);

  const total = await Place.countDocuments(query);

  res.json({
    success: true,
    data: {
      query: searchQuery,
      places,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    }
  });
});

// @desc    Get search suggestions/autocomplete
// @route   GET /api/search/suggestions
// @access  Public
export const getSearchSuggestions = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { q, type = 'all', limit = 10 } = req.query;
  const searchQuery = q as string;
  const searchType = type as string;
  const limitNum = parseInt(limit as string);

  const suggestions: any = {
    cities: [],
    places: []
  };

  // City suggestions
  if (searchType === 'all' || searchType === 'cities') {
    const cityRegex = new RegExp(searchQuery, 'i');
    const cities = await City.find({
      $or: [
        { name: cityRegex },
        { country: cityRegex }
      ],
      isPublished: true
    })
    .select('name slug country')
    .limit(Math.floor(limitNum / 2))
    .sort({ averageRating: -1 });

    suggestions.cities = cities;
  }

  // Place suggestions
  if (searchType === 'all' || searchType === 'places') {
    const placeRegex = new RegExp(searchQuery, 'i');
    const places = await Place.find({
      name: placeRegex,
      isPublished: true
    })
    .populate('city', 'name slug')
    .select('name slug category')
    .limit(Math.floor(limitNum / 2))
    .sort({ averageRating: -1 });

    suggestions.places = places;
  }

  res.json({
    success: true,
    data: {
      query: searchQuery,
      suggestions
    }
  });
});

// @desc    Get trending searches
// @route   GET /api/search/trending
// @access  Public
export const getTrendingSearches = asyncHandler(async (req: AuthRequest, res: Response) => {
  const limit = parseInt(req.query.limit as string) || 10;

  // This is a simplified implementation
  // In a real application, you would track search queries and their frequency
  const trendingCities = await City.find({ isPublished: true, isFeatured: true })
    .select('name slug')
    .limit(Math.floor(limit / 2))
    .sort({ averageRating: -1 });

  const trendingPlaces = await Place.find({ isPublished: true, isFeatured: true })
    .select('name slug category')
    .limit(Math.floor(limit / 2))
    .sort({ popularityScore: -1 });

  res.json({
    success: true,
    data: {
      trending: {
        cities: trendingCities,
        places: trendingPlaces
      }
    }
  });
});

// @desc    Search itineraries specifically
// @route   GET /api/search/itineraries
// @access  Public
export const searchItineraries = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { q, page = 1, limit = 20 } = req.query;
  const searchQuery = q as string;
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  // Search only public published itineraries
  const query: any = {
    $and: [
      { isPublic: true, status: 'published' },
      {
        $or: [
          { title: { $regex: searchQuery, $options: 'i' } },
          { description: { $regex: searchQuery, $options: 'i' } },
          { tags: { $in: [new RegExp(searchQuery, 'i')] } }
        ]
      }
    ]
  };

  const itineraries = await Itinerary.find(query)
    .populate('city', 'name country')
    .populate('user', 'name')
    .select('title description duration likes views city user createdAt tags')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limitNum);

  const total = await Itinerary.countDocuments(query);

  res.json({
    success: true,
    data: itineraries,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      pages: Math.ceil(total / limitNum)
    }
  });
});
