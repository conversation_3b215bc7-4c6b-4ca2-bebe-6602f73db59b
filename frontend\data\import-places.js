const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge';

// Simple place schema for importing
const placeSchema = new mongoose.Schema({
  name: String,
  slug: String,
  city: { type: mongoose.Schema.Types.ObjectId, ref: 'City' },
  category: String,
  subcategory: String,
  description: String,
  images: [{
    url: String,
    alt: String,
    caption: String,
    isPrimary: Boolean
  }],
  coordinates: {
    latitude: Number,
    longitude: Number
  },
  address: {
    street: String,
    area: String,
    city: String,
    state: String,
    country: String,
    postalCode: String
  },
  contact: {
    phone: String,
    email: String,
    website: String
  },
  timings: {
    openingHours: {
      monday: { open: String, close: String, isClosed: Boolean },
      tuesday: { open: String, close: String, isClosed: Boolean },
      wednesday: { open: String, close: String, isClosed: Boolean },
      thursday: { open: String, close: String, isClosed: Boolean },
      friday: { open: String, close: String, isClosed: Boolean },
      saturday: { open: String, close: String, isClosed: Boolean },
      sunday: { open: String, close: String, isClosed: Boolean }
    },
    specialHours: [{
      date: String,
      open: String,
      close: String,
      note: String
    }]
  },
  pricing: {
    entryFee: {
      adult: Number,
      child: Number,
      senior: Number,
      currency: String
    },
    priceRange: String,
    averageCost: Number
  },
  features: [String],
  cuisine: [String],
  historicalSignificance: {
    period: String,
    significance: String,
    historicalFacts: [String]
  },
  averageRating: { type: Number, default: 4.5 },
  totalReviews: { type: Number, default: 100 },
  popularityScore: { type: Number, default: 50 },
  isVerified: { type: Boolean, default: true },
  isPublished: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  tags: [String],
  seoMetadata: {
    title: String,
    description: String,
    keywords: [String]
  }
}, {
  timestamps: true
});

const Place = mongoose.model('Place', placeSchema);

// City schema for reference
const citySchema = new mongoose.Schema({
  name: String,
  slug: String
});

const City = mongoose.model('City', citySchema);

async function importPlaces() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    // Load places data
    const placesData = require('./places.json');
    console.log(`📍 Found ${placesData.length} places to import`);

    // Clear existing places
    console.log('🗑️ Clearing existing places...');
    await Place.deleteMany({});
    console.log('✅ Cleared existing places');

    // Import places
    let importedCount = 0;
    for (const placeData of placesData) {
      try {
        // Find the city by slug
        const city = await City.findOne({ slug: placeData.city });
        if (!city) {
          console.log(`❌ City not found for slug: ${placeData.city}`);
          continue;
        }

        // Replace city slug with city ObjectId
        const placeToImport = {
          ...placeData,
          city: city._id,
          seoMetadata: {
            title: `${placeData.name} - ${city.name} | HeritEdge`,
            description: placeData.description.substring(0, 160),
            keywords: placeData.tags || []
          }
        };

        const place = await Place.create(placeToImport);
        console.log(`✅ Imported: ${place.name} in ${city.name}`);
        importedCount++;
      } catch (error) {
        console.error(`❌ Failed to import place: ${placeData.name}`, error.message);
      }
    }

    console.log(`🎉 Successfully imported ${importedCount} places!`);
    
    // Verify import
    const totalPlaces = await Place.countDocuments();
    console.log(`📊 Total places in database: ${totalPlaces}`);

    // Show places by city
    const placesByCity = await Place.aggregate([
      {
        $lookup: {
          from: 'cities',
          localField: 'city',
          foreignField: '_id',
          as: 'cityInfo'
        }
      },
      {
        $group: {
          _id: '$cityInfo.name',
          count: { $sum: 1 },
          places: { $push: '$name' }
        }
      }
    ]);

    console.log('\n📍 Places by city:');
    placesByCity.forEach(city => {
      console.log(`  ${city._id}: ${city.count} places`);
      city.places.forEach(place => {
        console.log(`    - ${place}`);
      });
    });

    console.log('\n🌐 You can now view places at: http://localhost:3001/cities/[city-slug]');

  } catch (error) {
    console.error('❌ Failed to import places:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  importPlaces();
}

module.exports = { importPlaces };
