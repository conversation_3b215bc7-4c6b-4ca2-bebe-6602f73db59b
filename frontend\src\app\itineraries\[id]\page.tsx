'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import {
  CalendarDaysIcon,
  MapPinIcon,
  ClockIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  HeartIcon,
  EyeIcon,
  ShareIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartIconSolid,
  StarIcon as StarIconSolid
} from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import toast from 'react-hot-toast';

interface Itinerary {
  _id: string;
  title: string;
  description?: string;
  city: {
    _id: string;
    name: string;
    slug: string;
    country: string;
    images?: { url: string; alt: string }[];
  };
  user: {
    _id: string;
    name: string;
    email: string;
  };
  duration: number;
  startDate: string;
  endDate: string;
  budget: string;
  travelStyle: string;
  interests: string[];
  days: {
    dayNumber: number;
    date: string;
    title?: string;
    places: {
      place: {
        _id: string;
        name: string;
        slug: string;
        images?: { url: string; alt: string }[];
        category: string;
        averageRating?: number;
      };
      timeSlot: string;
      duration?: number;
      notes?: string;
      order: number;
    }[];
    notes?: string;
  }[];
  accommodationPreference?: string;
  transportPreference?: string;
  specialRequests?: string;
  status: string;
  isPublic: boolean;
  likes: string[];
  views: number;
  createdAt: string;
  updatedAt: string;
}

export default function ItineraryDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  
  const [itinerary, setItinerary] = useState<Itinerary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    const fetchItinerary = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await apiClient.getItinerary(id as string);
        
        if (response.success && response.data) {
          setItinerary(response.data.itinerary);
          setIsLiked(response.data.itinerary.likes.includes(user?.id));
        } else {
          setError('Itinerary not found');
        }
      } catch (err: any) {
        console.error('Error fetching itinerary:', err);
        setError(err.message || 'Failed to load itinerary');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchItinerary();
    }
  }, [id, user?.id]);

  const handleLike = async () => {
    if (!isAuthenticated) {
      toast.error('Please login to like itineraries');
      return;
    }

    try {
      setActionLoading(true);
      const response = await apiClient.toggleItineraryLike(id as string);
      
      if (response.success) {
        setIsLiked(response.data.isLiked);
        if (itinerary) {
          setItinerary(prev => prev ? {
            ...prev,
            likes: response.data.isLiked 
              ? [...prev.likes, user!.id]
              : prev.likes.filter(likeId => likeId !== user!.id)
          } : null);
        }
        toast.success(response.data.message);
      }
    } catch (error: any) {
      console.error('Like error:', error);
      toast.error(error.message || 'Failed to update like');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this itinerary? This action cannot be undone.')) {
      return;
    }

    try {
      await apiClient.deleteItinerary(id as string);
      toast.success('Itinerary deleted successfully');
      router.push('/itineraries');
    } catch (error: any) {
      console.error('Delete error:', error);
      toast.error(error.message || 'Failed to delete itinerary');
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: itinerary?.title,
          text: itinerary?.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  const getTravelStyleIcon = (style: string) => {
    switch (style) {
      case 'solo': return '🧳';
      case 'couple': return '💑';
      case 'family': return '👨‍👩‍👧‍👦';
      case 'group': return '👥';
      case 'business': return '💼';
      default: return '🧳';
    }
  };

  const getBudgetColor = (budget: string) => {
    switch (budget) {
      case 'budget': return 'bg-green-100 text-green-800';
      case 'mid-range': return 'bg-yellow-100 text-yellow-800';
      case 'luxury': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTimeSlotIcon = (timeSlot: string) => {
    switch (timeSlot) {
      case 'morning': return '🌅';
      case 'afternoon': return '☀️';
      case 'evening': return '🌆';
      case 'night': return '🌙';
      default: return '⏰';
    }
  };

  const getFallbackImage = (category?: string) => {
    const fallbacks = {
      museum: 'https://images.unsplash.com/photo-1566139884669-4b9356b4c040?w=400&q=80',
      religious: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&q=80',
      restaurant: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&q=80',
      attraction: 'https://images.unsplash.com/photo-1511739001486-6bfe10ce785f?w=400&q=80',
      default: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&q=80'
    };
    return fallbacks[category as keyof typeof fallbacks] || fallbacks.default;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded-xl mb-8"></div>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !itinerary) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Itinerary Not Found</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link
            href="/itineraries"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Itineraries
          </Link>
        </div>
      </div>
    );
  }

  const isOwner = user && itinerary.user._id === user.id;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">{itinerary.title}</h1>
              <div className="flex items-center space-x-6 text-blue-100 mb-4">
                <div className="flex items-center">
                  <MapPinIcon className="h-5 w-5 mr-2" />
                  <span>{itinerary.city.name}, {itinerary.city.country}</span>
                </div>
                <div className="flex items-center">
                  <ClockIcon className="h-5 w-5 mr-2" />
                  <span>{itinerary.duration} days</span>
                </div>
                <div className="flex items-center">
                  <span className="mr-2">{getTravelStyleIcon(itinerary.travelStyle)}</span>
                  <span className="capitalize">{itinerary.travelStyle}</span>
                </div>
              </div>
              {itinerary.description && (
                <p className="text-xl text-blue-100 max-w-2xl">{itinerary.description}</p>
              )}
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-center space-x-2 ml-6">
              <button
                onClick={handleLike}
                disabled={actionLoading}
                className="p-3 bg-white/10 backdrop-blur-sm rounded-full hover:bg-white/20 transition-colors disabled:opacity-50"
                title={isLiked ? 'Unlike itinerary' : 'Like itinerary'}
              >
                {actionLoading ? (
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                ) : isLiked ? (
                  <HeartIconSolid className="h-6 w-6 text-red-400" />
                ) : (
                  <HeartIcon className="h-6 w-6" />
                )}
              </button>
              
              <button
                onClick={handleShare}
                className="p-3 bg-white/10 backdrop-blur-sm rounded-full hover:bg-white/20 transition-colors"
                title="Share itinerary"
              >
                <ShareIcon className="h-6 w-6" />
              </button>
              
              {isOwner && (
                <>
                  <Link
                    href={`/itineraries/${itinerary._id}/edit`}
                    className="p-3 bg-white/10 backdrop-blur-sm rounded-full hover:bg-white/20 transition-colors"
                    title="Edit itinerary"
                  >
                    <PencilIcon className="h-6 w-6" />
                  </Link>
                  
                  <button
                    onClick={handleDelete}
                    className="p-3 bg-red-500/20 backdrop-blur-sm rounded-full hover:bg-red-500/30 transition-colors"
                    title="Delete itinerary"
                  >
                    <TrashIcon className="h-6 w-6" />
                  </button>
                </>
              )}
            </div>
          </div>
          
          {/* Stats */}
          <div className="flex items-center space-x-6 mt-6 text-sm text-blue-100">
            <div className="flex items-center">
              <HeartIconSolid className="h-4 w-4 mr-1 text-red-400" />
              <span>{itinerary.likes.length} likes</span>
            </div>
            <div className="flex items-center">
              <EyeIcon className="h-4 w-4 mr-1" />
              <span>{itinerary.views} views</span>
            </div>
            <div className="flex items-center">
              <UserGroupIcon className="h-4 w-4 mr-1" />
              <span>by {itinerary.user.name}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb items={[
            { label: 'Itineraries', href: '/itineraries' },
            { label: itinerary.title, isActive: true }
          ]} />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Trip Overview */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Trip Overview</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <CalendarDaysIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">Dates</div>
              <div className="text-sm text-gray-600">
                {new Date(itinerary.startDate).toLocaleDateString()} - {new Date(itinerary.endDate).toLocaleDateString()}
              </div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <CurrencyDollarIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">Budget</div>
              <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium capitalize ${getBudgetColor(itinerary.budget)}`}>
                {itinerary.budget.replace('-', ' ')}
              </span>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <UserGroupIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">Travel Style</div>
              <div className="text-sm text-gray-600 capitalize flex items-center justify-center">
                <span className="mr-1">{getTravelStyleIcon(itinerary.travelStyle)}</span>
                {itinerary.travelStyle}
              </div>
            </div>
          </div>

          {/* Interests */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 mb-3">Interests</h3>
            <div className="flex flex-wrap gap-2">
              {itinerary.interests.map((interest) => (
                <span key={interest} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm capitalize">
                  {interest}
                </span>
              ))}
            </div>
          </div>

          {/* Preferences */}
          {(itinerary.accommodationPreference || itinerary.transportPreference || itinerary.specialRequests) && (
            <div className="border-t border-gray-200 pt-6">
              <h3 className="font-semibold text-gray-900 mb-3">Preferences</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {itinerary.accommodationPreference && (
                  <div>
                    <span className="font-medium text-gray-700">Accommodation:</span>
                    <span className="ml-2 text-gray-600 capitalize">{itinerary.accommodationPreference}</span>
                  </div>
                )}
                {itinerary.transportPreference && (
                  <div>
                    <span className="font-medium text-gray-700">Transport:</span>
                    <span className="ml-2 text-gray-600 capitalize">{itinerary.transportPreference}</span>
                  </div>
                )}
              </div>
              {itinerary.specialRequests && (
                <div className="mt-3">
                  <span className="font-medium text-gray-700">Special Requests:</span>
                  <p className="mt-1 text-gray-600">{itinerary.specialRequests}</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Daily Itinerary */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-gray-900">Daily Itinerary</h2>
          
          {itinerary.days.length > 0 ? (
            itinerary.days.map((day) => (
              <div key={day.dayNumber} className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold">Day {day.dayNumber}</h3>
                      <p className="text-blue-100">{new Date(day.date).toLocaleDateString('en-US', { 
                        weekday: 'long', 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}</p>
                      {day.title && day.title !== `Day ${day.dayNumber}` && (
                        <p className="text-lg font-medium mt-1">{day.title}</p>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  {day.places.length > 0 ? (
                    <div className="space-y-4">
                      {day.places
                        .sort((a, b) => a.order - b.order)
                        .map((placeItem, index) => (
                        <div key={index} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="flex-shrink-0">
                            <img
                              src={placeItem.place.images?.[0]?.url || getFallbackImage(placeItem.place.category)}
                              alt={placeItem.place.name}
                              className="w-16 h-16 rounded-lg object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = getFallbackImage(placeItem.place.category);
                              }}
                            />
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <div>
                                <h4 className="font-semibold text-gray-900 hover:text-blue-600">
                                  <Link href={`/places/${placeItem.place.slug}`}>
                                    {placeItem.place.name}
                                  </Link>
                                </h4>
                                <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                  <span className="flex items-center">
                                    <span className="mr-1">{getTimeSlotIcon(placeItem.timeSlot)}</span>
                                    <span className="capitalize">{placeItem.timeSlot}</span>
                                  </span>
                                  {placeItem.duration && (
                                    <span className="flex items-center">
                                      <ClockIcon className="h-4 w-4 mr-1" />
                                      <span>{placeItem.duration}h</span>
                                    </span>
                                  )}
                                  {placeItem.place.averageRating && (
                                    <span className="flex items-center">
                                      <StarIconSolid className="h-4 w-4 mr-1 text-yellow-400" />
                                      <span>{placeItem.place.averageRating}</span>
                                    </span>
                                  )}
                                </div>
                                {placeItem.notes && (
                                  <p className="text-sm text-gray-600 mt-2">{placeItem.notes}</p>
                                )}
                              </div>
                              
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium capitalize">
                                {placeItem.place.category}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <InformationCircleIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                      <p>No places planned for this day yet.</p>
                      {isOwner && (
                        <Link
                          href={`/itineraries/${itinerary._id}/edit`}
                          className="inline-block mt-2 text-blue-600 hover:text-blue-700 font-medium"
                        >
                          Add places to this day
                        </Link>
                      )}
                    </div>
                  )}
                  
                  {day.notes && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <h5 className="font-medium text-yellow-800 mb-1">Day Notes:</h5>
                      <p className="text-yellow-700 text-sm">{day.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="bg-white rounded-xl shadow-sm p-8 text-center">
              <CalendarDaysIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Daily Plans Yet</h3>
              <p className="text-gray-600 mb-6">
                This itinerary doesn&apos;t have detailed daily plans yet.
              </p>
              {isOwner && (
                <Link
                  href={`/itineraries/${itinerary._id}/edit`}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <PencilIcon className="h-5 w-5 mr-2" />
                  Add Daily Plans
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
