# Node.js dependencies
node_modules/
frontend/node_modules/
backend/node_modules/
backend/data/
backend/scripts/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Debug logs
logs/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Next.js
frontend/.next/
frontend/out/
frontend/build/
frontend/dist/

# Production builds
backend/dist/
backend/build/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Database
*.sqlite
*.db

# Backup files
*.bak
*.backup

# Package lock files (choose one)
# package-lock.json
# yarn.lock

# Local development
.local
.cache

# MongoDB data (if running locally)
data/db/

# SSL certificates
*.pem
*.key
*.crt

# Docker
.dockerignore
Dockerfile.dev

# Testing
coverage/
.nyc_output/

# Misc
*.tgz
*.tar.gz
.npm
.eslintcache
