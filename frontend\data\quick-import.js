const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge';

async function quickImportPlaces() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    // Get collections
    const citiesCollection = mongoose.connection.db.collection('cities');
    const placesCollection = mongoose.connection.db.collection('places');

    // Find Paris city
    const parisCity = await citiesCollection.findOne({ slug: 'paris' });
    if (!parisCity) {
      console.log('❌ Paris city not found. Please make sure Paris city exists in the database.');
      return;
    }

    console.log(`✅ Found Paris city: ${parisCity.name} (ID: ${parisCity._id})`);

    // Load places data
    const placesData = require('./places-clean.json');
    
    // Clear existing places for Paris
    await placesCollection.deleteMany({ city: parisCity._id });
    console.log('🗑️ Cleared existing places for Paris');

    // Update places data with correct city ObjectId
    const updatedPlaces = placesData.map(place => ({
      ...place,
      city: parisCity._id,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    // Insert places
    const result = await placesCollection.insertMany(updatedPlaces);
    console.log(`✅ Successfully imported ${result.insertedCount} places`);

    // Verify and display results
    const totalPlaces = await placesCollection.countDocuments({ city: parisCity._id });
    console.log(`📊 Total places for Paris: ${totalPlaces}`);

    // Group by category
    const placesByCategory = await placesCollection.aggregate([
      { $match: { city: parisCity._id } },
      { $group: { _id: '$category', count: { $sum: 1 }, places: { $push: '$name' } } },
      { $sort: { _id: 1 } }
    ]).toArray();

    console.log('\n📍 Places by category:');
    placesByCategory.forEach(category => {
      console.log(`\n🏷️  ${category._id.toUpperCase()} (${category.count} places):`);
      category.places.forEach(placeName => {
        console.log(`   - ${placeName}`);
      });
    });

    console.log('\n🎉 Places import completed successfully!');
    console.log('🌐 You can now view places at: http://localhost:3001/cities/paris');
    console.log('📱 Click the "Places" tab to see the categorized places in a 2x2 grid layout');

  } catch (error) {
    console.error('❌ Failed to import places:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  quickImportPlaces();
}

module.exports = { quickImportPlaces };
