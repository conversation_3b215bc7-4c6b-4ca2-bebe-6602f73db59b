#!/bin/bash

# HeritEdge Kubernetes Cleanup Script
echo "🗑️  Cleaning up HeritEdge Kubernetes deployment..."

# Set error handling
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed. Please install kubectl and try again."
    exit 1
fi

# Confirm deletion
read -p "Are you sure you want to delete the entire HeritEdge deployment? This will remove all data! (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Cleanup cancelled."
    exit 0
fi

print_warning "Starting cleanup process..."

# Delete deployments
print_status "Deleting deployments..."
kubectl delete -f k8s/admin-deployment.yaml --ignore-not-found=true
kubectl delete -f k8s/frontend-deployment.yaml --ignore-not-found=true
kubectl delete -f k8s/backend-deployment.yaml --ignore-not-found=true
kubectl delete -f k8s/mongodb-deployment.yaml --ignore-not-found=true

# Delete persistent volumes
print_status "Deleting persistent volumes..."
kubectl delete -f k8s/persistent-volumes.yaml --ignore-not-found=true

# Delete ConfigMaps and Secrets
print_status "Deleting ConfigMaps and Secrets..."
kubectl delete -f k8s/configmap.yaml --ignore-not-found=true
kubectl delete -f k8s/secrets.yaml --ignore-not-found=true

# Delete namespace (this will delete everything in the namespace)
print_status "Deleting namespace..."
kubectl delete namespace heritedge --ignore-not-found=true

# Wait for namespace deletion
print_status "Waiting for namespace deletion to complete..."
kubectl wait --for=delete namespace/heritedge --timeout=120s || true

print_success "Cleanup completed! 🎉"
print_status "All HeritEdge resources have been removed from Kubernetes."

# Optional: Clean up Docker images
read -p "Do you also want to remove the Docker images? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Removing Docker images..."
    docker rmi heritedge/backend:latest heritedge/frontend:latest heritedge/admin:latest 2>/dev/null || true
    print_success "Docker images removed."
fi

print_status "Cleanup process completed."
