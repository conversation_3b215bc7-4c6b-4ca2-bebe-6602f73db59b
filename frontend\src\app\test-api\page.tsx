'use client';

import { useState, useEffect } from 'react';
import apiClient from '@/lib/api';

export default function TestApiPage() {
  const [apiStatus, setApiStatus] = useState<string>('Testing...');
  const [apiData, setApiData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    testApiConnection();
  }, []);

  const testApiConnection = async () => {
    try {
      setApiStatus('Connecting to API...');
      setError(null);

      // Test the basic API endpoint
      const response = await fetch(process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') + '/api' || 'http://localhost:5001/api');
      
      if (response.ok) {
        const data = await response.json();
        setApiData(data);
        setApiStatus('✅ API Connection Successful!');
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err: any) {
      setError(err.message);
      setApiStatus('❌ API Connection Failed');
    }
  };

  const testCitiesEndpoint = async () => {
    try {
      setApiStatus('Testing cities endpoint...');
      setError(null);

      const response = await apiClient.getCities();
      setApiData(response);
      setApiStatus('✅ Cities endpoint working!');
    } catch (err: any) {
      setError(err.message);
      setApiStatus('❌ Cities endpoint failed');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">API Connection Test</h1>
          
          <div className="space-y-6">
            <div className="border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
              <p className="text-lg mb-4">{apiStatus}</p>
              
              <div className="flex space-x-4">
                <button
                  onClick={testApiConnection}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  Test Basic API
                </button>
                <button
                  onClick={testCitiesEndpoint}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                >
                  Test Cities Endpoint
                </button>
              </div>
            </div>

            {error && (
              <div className="border border-red-300 rounded-lg p-6 bg-red-50">
                <h3 className="text-lg font-semibold text-red-800 mb-2">Error Details</h3>
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {apiData && (
              <div className="border border-green-300 rounded-lg p-6 bg-green-50">
                <h3 className="text-lg font-semibold text-green-800 mb-2">API Response</h3>
                <pre className="text-sm text-green-700 overflow-auto">
                  {JSON.stringify(apiData, null, 2)}
                </pre>
              </div>
            )}

            <div className="border rounded-lg p-6 bg-gray-50">
              <h3 className="text-lg font-semibold mb-2">Configuration</h3>
              <p><strong>API URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api'}</p>
              <p><strong>Frontend URL:</strong> {process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3001'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
