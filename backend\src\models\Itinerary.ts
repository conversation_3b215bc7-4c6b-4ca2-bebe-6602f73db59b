import mongoose, { Document, Schema } from 'mongoose';

export interface IItinerary extends Document {
  _id: mongoose.Types.ObjectId;
  title: string;
  description?: string;
  user: mongoose.Types.ObjectId;
  city: mongoose.Types.ObjectId;
  duration: number; // in days
  startDate: Date;
  endDate: Date;
  budget: 'budget' | 'mid-range' | 'luxury';
  travelStyle: 'solo' | 'couple' | 'family' | 'group' | 'business';
  interests: string[];

  // Enhanced day structure
  days: {
    dayNumber: number;
    date: Date;
    title?: string;
    places: {
      place: mongoose.Types.ObjectId;
      timeSlot: 'morning' | 'afternoon' | 'evening' | 'night';
      duration?: number; // in hours
      notes?: string;
      order: number;
    }[];
    notes?: string;
  }[];

  // Preferences
  accommodationPreference?: string;
  transportPreference?: string;
  specialRequests?: string;

  // Enhanced metadata
  tags: string[];
  isPublic: boolean;
  isTemplate: boolean;
  likes: mongoose.Types.ObjectId[];
  saves: mongoose.Types.ObjectId[];
  views: number;
  rating?: number;
  totalRatings: number;
  status: 'draft' | 'published' | 'archived';

  // AI generation flag
  isAIGenerated: boolean;
  generationPrompt?: string;

  createdAt: Date;
  updatedAt: Date;
}

const itinerarySchema = new Schema<IItinerary>({
  title: {
    type: String,
    required: [true, 'Itinerary title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  city: {
    type: Schema.Types.ObjectId,
    ref: 'City',
    required: true
  },
  duration: {
    type: Number,
    required: [true, 'Duration is required'],
    min: [1, 'Duration must be at least 1 day'],
    max: [30, 'Duration cannot exceed 30 days']
  },
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required']
  },
  budget: {
    type: String,
    enum: ['budget', 'mid-range', 'luxury'],
    required: true
  },
  travelStyle: {
    type: String,
    enum: ['solo', 'couple', 'family', 'group', 'business'],
    required: true
  },
  interests: [{
    type: String,
    required: true
  }],
  days: [{
    dayNumber: {
      type: Number,
      required: true,
      min: 1
    },
    date: {
      type: Date,
      required: true
    },
    title: {
      type: String,
      maxlength: 100
    },
    places: [{
      place: {
        type: Schema.Types.ObjectId,
        ref: 'Place',
        required: true
      },
      timeSlot: {
        type: String,
        enum: ['morning', 'afternoon', 'evening', 'night'],
        required: true
      },
      duration: {
        type: Number,
        min: 0.5,
        max: 12
      },
      notes: {
        type: String,
        maxlength: 500
      },
      order: {
        type: Number,
        required: true,
        min: 1
      }
    }],
    notes: {
      type: String,
      maxlength: 1000
    }
  }],

  // Preferences
  accommodationPreference: {
    type: String,
    maxlength: 100
  },
  transportPreference: {
    type: String,
    maxlength: 100
  },
  specialRequests: {
    type: String,
    maxlength: 1000
  },

  tags: [{
    type: String,
    trim: true,
    maxlength: 50
  }],
  isPublic: {
    type: Boolean,
    default: false
  },
  isTemplate: {
    type: Boolean,
    default: false
  },
  likes: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  saves: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  views: {
    type: Number,
    default: 0
  },
  rating: {
    type: Number,
    min: 1,
    max: 5
  },
  totalRatings: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },

  // AI generation
  isAIGenerated: {
    type: Boolean,
    default: false
  },
  generationPrompt: {
    type: String,
    maxlength: 2000
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
itinerarySchema.index({ user: 1, createdAt: -1 });
itinerarySchema.index({ city: 1, isPublic: 1 });
itinerarySchema.index({ tags: 1 });
itinerarySchema.index({ isPublic: 1, status: 1, rating: -1 });
itinerarySchema.index({ travelStyle: 1, duration: 1 });

// Virtual for total likes
itinerarySchema.virtual('totalLikes').get(function(this: IItinerary) {
  return this.likes.length;
});

// Virtual for total saves
itinerarySchema.virtual('totalSaves').get(function(this: IItinerary) {
  return this.saves.length;
});

// Virtual for total places
itinerarySchema.virtual('totalPlaces').get(function(this: IItinerary) {
  return this.days.reduce((total, day) => total + day.places.length, 0);
});

// Pre-save middleware to validate days
itinerarySchema.pre('save', function(this: IItinerary, next) {
  // Ensure days are numbered correctly
  this.days.sort((a, b) => a.dayNumber - b.dayNumber);

  // Validate day numbers are sequential (only if days exist)
  if (this.days.length > 0) {
    for (let i = 0; i < this.days.length; i++) {
      if (this.days[i].dayNumber !== i + 1) {
        return next(new Error('Day numbers must be sequential starting from 1'));
      }
    }
  }

  // Validate duration matches number of days (only if days exist)
  if (this.days.length > 0 && this.days.length !== this.duration) {
    return next(new Error('Number of days must match duration'));
  }

  next();
});

export const Itinerary = mongoose.model<IItinerary>('Itinerary', itinerarySchema);
