const fs = require('fs');

// Read the search page file
const filePath = 'src/app/search/page.tsx';
let content = fs.readFileSync(filePath, 'utf8');

// Replace all HTML entities with proper JSX
content = content.replace(/&quot;/g, '"');
content = content.replace(/&apos;/g, "'");

// Fix specific issues
content = content.replace(/couldn't find any cities matching/g, "couldn&apos;t find any cities matching");

// Write back the file
fs.writeFileSync(filePath, content, 'utf8');

console.log('Fixed search page HTML entities');
