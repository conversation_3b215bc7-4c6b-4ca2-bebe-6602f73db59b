import express from 'express';
import { query, param } from 'express-validator';
import {
  getUserProfile,
  getUserBookmarks,
  getUserContributions,
  updateUserPreferences
} from '../controllers/userController';
import { protect, authorize } from '../middleware/auth';

const router = express.Router();

// @route   GET /api/users/:userId
// @desc    Get user profile
// @access  Public (if profile is public) / Private (own profile)
router.get('/:userId', [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID')
], getUserProfile);

// @route   GET /api/users/:userId/bookmarks
// @desc    Get user bookmarks
// @access  Private (own bookmarks only)
router.get('/:userId/bookmarks', protect, [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID'),
  query('type')
    .optional()
    .isIn(['cities', 'places', 'all'])
    .withMessage('Type must be cities, places, or all'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getUserBookmarks);

// @route   GET /api/users/:userId/contributions
// @desc    Get user contributions
// @access  Public (if profile is public) / Private (own contributions)
router.get('/:userId/contributions', [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getUserContributions);

// @route   PUT /api/users/:userId/preferences
// @desc    Update user preferences
// @access  Private (own preferences only)
router.put('/:userId/preferences', protect, [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID')
], updateUserPreferences);

export default router;
