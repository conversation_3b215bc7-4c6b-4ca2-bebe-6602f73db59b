const mongoose = require('mongoose');

// Simple script to reset all ratings to 0
const resetRatings = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/heritedge');
    console.log('✅ Connected to MongoDB');

    // Get the collections directly
    const db = mongoose.connection.db;
    const citiesCollection = db.collection('cities');
    const placesCollection = db.collection('places');

    console.log('🔄 Resetting city ratings...');
    // Reset all city ratings
    const cityResult = await citiesCollection.updateMany(
      {},
      {
        $set: {
          averageRating: 0,
          totalReviews: 0
        }
      }
    );
    console.log(`✅ Updated ${cityResult.modifiedCount} cities`);

    console.log('🔄 Resetting place ratings...');
    // Reset all place ratings
    const placeResult = await placesCollection.updateMany(
      {},
      {
        $set: {
          averageRating: 0,
          totalReviews: 0
        }
      }
    );
    console.log(`✅ Updated ${placeResult.modifiedCount} places`);

    console.log('🎉 All ratings reset to 0');

    // Close connection
    await mongoose.connection.close();
    console.log('✅ Disconnected from MongoDB');

    process.exit(0);

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
};

// Run the script
resetRatings();
