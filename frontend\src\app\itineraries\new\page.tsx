'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import {
  SparklesIcon,
  InformationCircleIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import { City } from '@/types';
import toast from 'react-hot-toast';

interface ItineraryFormData {
  title: string;
  description: string;
  cityId: string;
  duration: number;
  startDate: string;
  endDate: string;
  travelStyle: 'solo' | 'couple' | 'family' | 'group' | 'business';
  budget: 'budget' | 'mid-range' | 'luxury';
  interests: string[];
  isPublic: boolean;
  accommodationPreference: string;
  transportPreference: string;
  specialRequests: string;
}



export default function CreateItineraryPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<ItineraryFormData>({
    title: '',
    description: '',
    cityId: '',
    duration: 3,
    startDate: '',
    endDate: '',
    travelStyle: 'solo',
    budget: 'mid-range',
    interests: [],
    isPublic: false, // Keep itineraries private
    accommodationPreference: '',
    transportPreference: '',
    specialRequests: ''
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch cities
  useEffect(() => {
    const fetchCities = async () => {
      try {
        const response = await apiClient.getCities();
        if (response.success && response.data) {
          setCities(response.data.cities || []);
        }
      } catch (error) {
        console.error('Error fetching cities:', error);
      }
    };

    fetchCities();
  }, []);

  // Auto-calculate end date when duration changes
  useEffect(() => {
    if (formData.startDate && formData.duration) {
      const startDate = new Date(formData.startDate);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + formData.duration - 1);
      setFormData(prev => ({
        ...prev,
        endDate: endDate.toISOString().split('T')[0]
      }));
    }
  }, [formData.startDate, formData.duration]);

  const interestOptions = [
    'history', 'culture', 'food', 'adventure', 'shopping', 'nature', 
    'photography', 'art', 'architecture', 'nightlife', 'museums', 
    'religious sites', 'local experiences', 'festivals', 'sports'
  ];

  const handleInterestToggle = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const handleSubmit = async (e: React.FormEvent | null, useAI: boolean = false) => {
    if (e) e.preventDefault();

    if (!formData.title || !formData.cityId || !formData.startDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.interests.length === 0) {
      toast.error('Please select at least one interest');
      return;
    }

    try {
      setLoading(true);

      if (useAI) {
        // First generate AI recommendations
        const aiResponse = await apiClient.generateItineraryRecommendations({
          cityId: formData.cityId,
          duration: formData.duration,
          startDate: formData.startDate,
          budget: formData.budget,
          travelStyle: formData.travelStyle,
          interests: formData.interests,
          accommodationPreference: formData.accommodationPreference,
          transportPreference: formData.transportPreference,
          specialRequests: formData.specialRequests
        });

        if (aiResponse.success && aiResponse.data) {
          // Create itinerary with AI-generated days
          const response = await apiClient.createItinerary({
            ...formData,
            days: aiResponse.data.days,
            isAIGenerated: true,
            generationPrompt: aiResponse.data.generationPrompt,
            userId: user?.id
          });

          if (response.success && response.data) {
            toast.success('AI-powered itinerary created successfully!');
            router.push(`/itineraries/${response.data.itinerary._id}`);
          }
        }
      } else {
        // Create empty itinerary
        const response = await apiClient.createItinerary({
          ...formData,
          userId: user?.id
        });

        if (response.success && response.data) {
          toast.success('Itinerary created successfully!');
          router.push(`/itineraries/${response.data.itinerary._id}/edit`);
        }
      }
    } catch (error: any) {
      console.error('Create itinerary error:', error);
      toast.error(error.message || 'Failed to create itinerary');
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (step === 1 && (!formData.title || !formData.cityId)) {
      toast.error('Please fill in the basic information');
      return;
    }
    if (step === 2 && (!formData.startDate || formData.duration < 1)) {
      toast.error('Please set valid dates and duration');
      return;
    }
    setStep(prev => Math.min(prev + 1, 4));
  };

  const prevStep = () => {
    setStep(prev => Math.max(prev - 1, 1));
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const stepTitles = [
    'Basic Information',
    'Travel Details', 
    'Preferences',
    'Review & Create'
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <SparklesIcon className="h-16 w-16 text-white mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Create Your Itinerary</h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Plan your perfect cultural journey with personalized recommendations
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb items={[
            { label: 'Itineraries', href: '/itineraries' },
            { label: 'Create New', isActive: true }
          ]} />
        </div>
      </div>

      {/* Progress Steps */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {stepTitles.map((title, index) => (
              <div key={index} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  step > index + 1 
                    ? 'bg-green-500 border-green-500 text-white'
                    : step === index + 1
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : 'bg-white border-gray-300 text-gray-500'
                }`}>
                  {step > index + 1 ? '✓' : index + 1}
                </div>
                <span className={`ml-2 text-sm font-medium ${
                  step >= index + 1 ? 'text-gray-900' : 'text-gray-500'
                }`}>
                  {title}
                </span>
                {index < stepTitles.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    step > index + 1 ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <div className="bg-white rounded-xl shadow-sm p-8">
          <form onSubmit={handleSubmit}>
            {/* Step 1: Basic Information */}
            {step === 1 && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Basic Information</h2>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Itinerary Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Amazing Paris Adventure"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Destination City *
                  </label>
                  <select
                    value={formData.cityId}
                    onChange={(e) => setFormData(prev => ({ ...prev, cityId: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select a city</option>
                    {cities.map((city) => {
                      const cityId = city.id || (city as any)._id;
                      return (
                        <option key={cityId} value={cityId}>
                          {city.name}, {city.country}
                        </option>
                      );
                    })}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    placeholder="Describe your trip goals and expectations..."
                  />
                </div>
              </div>
            )}

            {/* Step 2: Travel Details */}
            {step === 2 && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Travel Details</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Start Date *
                    </label>
                    <input
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration (Days) *
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="30"
                      value={formData.duration}
                      onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 1 }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date (Auto-calculated)
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                    disabled
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Travel Style
                    </label>
                    <select
                      value={formData.travelStyle}
                      onChange={(e) => setFormData(prev => ({ ...prev, travelStyle: e.target.value as any }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="solo">Solo Travel</option>
                      <option value="couple">Couple</option>
                      <option value="family">Family</option>
                      <option value="group">Group</option>
                      <option value="business">Business</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Budget Level
                    </label>
                    <select
                      value={formData.budget}
                      onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value as any }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="budget">Budget-Friendly</option>
                      <option value="mid-range">Mid-Range</option>
                      <option value="luxury">Luxury</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Preferences */}
            {step === 3 && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Preferences</h2>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-4">
                    What are you interested in? (Select all that apply) *
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {interestOptions.map((interest) => (
                      <label key={interest} className="flex items-center space-x-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.interests.includes(interest)}
                          onChange={() => handleInterestToggle(interest)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="text-sm font-medium text-gray-700 capitalize">{interest}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Accommodation Preference
                  </label>
                  <select
                    value={formData.accommodationPreference}
                    onChange={(e) => setFormData(prev => ({ ...prev, accommodationPreference: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">No preference</option>
                    <option value="hotel">Hotel</option>
                    <option value="hostel">Hostel</option>
                    <option value="apartment">Apartment/Airbnb</option>
                    <option value="boutique">Boutique Hotel</option>
                    <option value="resort">Resort</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Transportation Preference
                  </label>
                  <select
                    value={formData.transportPreference}
                    onChange={(e) => setFormData(prev => ({ ...prev, transportPreference: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">No preference</option>
                    <option value="walking">Walking</option>
                    <option value="public">Public Transport</option>
                    <option value="taxi">Taxi/Rideshare</option>
                    <option value="rental">Car Rental</option>
                    <option value="bike">Bicycle</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Requests or Requirements
                  </label>
                  <textarea
                    value={formData.specialRequests}
                    onChange={(e) => setFormData(prev => ({ ...prev, specialRequests: e.target.value }))}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    placeholder="Any dietary restrictions, accessibility needs, or special interests..."
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isPublic"
                    checked={formData.isPublic}
                    onChange={(e) => setFormData(prev => ({ ...prev, isPublic: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isPublic" className="text-sm font-medium text-gray-700">
                    Make this itinerary public (others can view and like it)
                  </label>
                </div>
              </div>
            )}

            {/* Step 4: Review & Create */}
            {step === 4 && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Review Your Itinerary</h2>
                </div>

                <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-semibold text-gray-900">Title</h3>
                      <p className="text-gray-600">{formData.title}</p>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Destination</h3>
                      <p className="text-gray-600">
                        {(() => {
                          const selectedCity = cities.find(c => (c.id || (c as any)._id) === formData.cityId);
                          return selectedCity ? `${selectedCity.name}, ${selectedCity.country}` : 'City not found';
                        })()}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Duration</h3>
                      <p className="text-gray-600">{formData.duration} days</p>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Dates</h3>
                      <p className="text-gray-600">
                        {new Date(formData.startDate).toLocaleDateString()} - {new Date(formData.endDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Travel Style</h3>
                      <p className="text-gray-600 capitalize">{formData.travelStyle}</p>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Budget</h3>
                      <p className="text-gray-600 capitalize">{formData.budget.replace('-', ' ')}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Interests</h3>
                    <div className="flex flex-wrap gap-2">
                      {formData.interests.map((interest) => (
                        <span key={interest} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm capitalize">
                          {interest}
                        </span>
                      ))}
                    </div>
                  </div>

                  {formData.description && (
                    <div>
                      <h3 className="font-semibold text-gray-900">Description</h3>
                      <p className="text-gray-600">{formData.description}</p>
                    </div>
                  )}

                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>Visibility: {formData.isPublic ? 'Public' : 'Private'}</span>
                    {formData.accommodationPreference && (
                      <span>Accommodation: {formData.accommodationPreference}</span>
                    )}
                    {formData.transportPreference && (
                      <span>Transport: {formData.transportPreference}</span>
                    )}
                  </div>
                </div>

                {/* Creation Options */}
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose How to Create Your Itinerary</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* AI Generation Option */}
                    <div className="bg-white rounded-lg p-4 border-2 border-purple-200 hover:border-purple-400 transition-colors">
                      <div className="flex items-center mb-3">
                        <SparklesIcon className="h-6 w-6 text-purple-600 mr-2" />
                        <h4 className="font-semibold text-gray-900">AI-Powered Generation</h4>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">
                        Let our AI create a detailed day-by-day itinerary with recommended places, activities, and timing based on your preferences.
                      </p>
                      <ul className="text-xs text-gray-500 space-y-1 mb-4">
                        <li>• Personalized recommendations</li>
                        <li>• Optimized timing and routes</li>
                        <li>• Cultural tips and local insights</li>
                        <li>• Budget-appropriate suggestions</li>
                      </ul>
                    </div>

                    {/* Manual Creation Option */}
                    <div className="bg-white rounded-lg p-4 border-2 border-gray-200 hover:border-gray-400 transition-colors">
                      <div className="flex items-center mb-3">
                        <PencilIcon className="h-6 w-6 text-gray-600 mr-2" />
                        <h4 className="font-semibold text-gray-900">Start from Scratch</h4>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">
                        Create an empty itinerary framework and manually add places and activities to each day.
                      </p>
                      <ul className="text-xs text-gray-500 space-y-1 mb-4">
                        <li>• Full control over planning</li>
                        <li>• Add places as you research</li>
                        <li>• Customize every detail</li>
                        <li>• Build at your own pace</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">You can always edit later!</h4>
                      <p className="text-blue-700 text-sm mt-1">
                        Whether you choose AI generation or manual creation, you can always edit, add, remove, or rearrange
                        places and activities in your itinerary after it&apos;s created.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-8 border-t border-gray-200 mt-8">
              <button
                type="button"
                onClick={prevStep}
                disabled={step === 1}
                className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              {step < 4 ? (
                <button
                  type="button"
                  onClick={nextStep}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Next
                </button>
              ) : (
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => handleSubmit(null, true)} // AI generation
                    disabled={loading}
                    className="flex items-center space-x-2 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Generating...</span>
                      </>
                    ) : (
                      <>
                        <SparklesIcon className="h-5 w-5" />
                        <span>Generate with AI</span>
                      </>
                    )}
                  </button>

                  <button
                    type="submit"
                    disabled={loading}
                    className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Creating...</span>
                      </>
                    ) : (
                      <>
                        <PencilIcon className="h-5 w-5" />
                        <span>Create Empty</span>
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
