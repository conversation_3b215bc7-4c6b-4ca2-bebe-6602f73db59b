import { Response } from 'express';
import { validationResult } from 'express-validator';
import Place from '../models/Place';
import City from '../models/City';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

// @desc    Get all places
// @route   GET /api/places
// @access  Public
export const getPlaces = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const skip = (page - 1) * limit;

  // Build query
  const query: any = { isPublished: true };
  
  if (req.query.city) {
    query.city = req.query.city;
  }
  
  if (req.query.category) {
    query.category = req.query.category;
  }
  
  if (req.query.search) {
    query.$text = { $search: req.query.search as string };
  }

  if (req.query.minRating) {
    query.averageRating = { $gte: parseFloat(req.query.minRating as string) };
  }

  if (req.query.priceRange) {
    query['pricing.priceRange'] = req.query.priceRange;
  }

  // Build sort
  let sort: any = { createdAt: -1 };
  if (req.query.sort) {
    const sortField = req.query.sort as string;
    sort = {};
    if (sortField.startsWith('-')) {
      sort[sortField.substring(1)] = -1;
    } else {
      sort[sortField] = 1;
    }
  }

  const places = await Place.find(query)
    .sort(sort)
    .skip(skip)
    .limit(limit)
    .populate('city', 'name slug country')
    .populate('createdBy', 'name')
    .select('-reviews -__v');

  const total = await Place.countDocuments(query);

  res.json({
    success: true,
    data: {
      places,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get single place
// @route   GET /api/places/:slug
// @access  Public
export const getPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  const place = await Place.findOne({ slug: req.params.slug, isPublished: true })
    .populate('city', 'name slug country')
    .populate('createdBy', 'name avatar')
    .populate('reviews.user', 'name avatar');

  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  res.json({
    success: true,
    data: {
      place
    }
  });
});

// @desc    Create place
// @route   POST /api/places
// @access  Private
export const createPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  // Check if city exists
  const city = await City.findById(req.body.city);
  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Generate slug from name
  const slug = req.body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
  
  const placeData = {
    ...req.body,
    slug,
    createdBy: req.user!._id,
    lastUpdatedBy: req.user!._id
  };

  const place = await Place.create(placeData);

  // Add place to city's places array
  city.places.push(place._id);
  await city.save();

  res.status(201).json({
    success: true,
    message: 'Place created successfully',
    data: {
      place
    }
  });
});

// Placeholder implementations for other methods
export const updatePlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Update place - not implemented yet' });
});

export const deletePlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Delete place - not implemented yet' });
});

export const addReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Add review - not implemented yet' });
});

export const updateReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Update review - not implemented yet' });
});

export const deleteReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Delete review - not implemented yet' });
});

export const bookmarkPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Bookmark place - not implemented yet' });
});

export const unbookmarkPlace = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Unbookmark place - not implemented yet' });
});

export const getNearbyPlaces = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { latitude, longitude } = req.query;
  const radius = parseFloat(req.query.radius as string) || 10; // Default 10km
  const limit = parseInt(req.query.limit as string) || 20;

  const query: any = {
    isPublished: true,
    coordinates: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [parseFloat(longitude as string), parseFloat(latitude as string)]
        },
        $maxDistance: radius * 1000 // Convert km to meters
      }
    }
  };

  if (req.query.category) {
    query.category = req.query.category;
  }

  const places = await Place.find(query)
    .limit(limit)
    .populate('city', 'name slug')
    .select('-reviews -__v');

  res.json({
    success: true,
    data: {
      places
    }
  });
});
