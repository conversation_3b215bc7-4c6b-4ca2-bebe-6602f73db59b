// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: 'user' | 'admin' | 'moderator';
  isEmailVerified: boolean;
  profile: {
    bio?: string;
    location?: string;
    website?: string;
    socialLinks?: {
      twitter?: string;
      instagram?: string;
      facebook?: string;
    };
  };
  preferences: {
    newsletter: boolean;
    notifications: boolean;
    publicProfile: boolean;
  };
  bookmarkedCities: string[];
  bookmarkedPlaces: string[];
  contributedPlaces: string[];
  visitedPlaces: VisitedPlace[];
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Visited Place Types
export interface VisitedPlace {
  _id?: string;
  place: Place;
  visitDate: string;
  rating?: number;
  review?: string;
}

export interface VisitedPlacesResponse {
  visitedPlaces: VisitedPlace[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  stats: {
    totalVisited: number;
    averageRating: number;
    categoriesVisited: number;
    countriesVisited: number;
    citiesVisited: number;
  };
}

export interface VisitedPlaceFilters {
  search?: string;
  category?: string;
  rating?: number;
  sortBy?: 'visitDate' | 'rating' | 'name';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// City Types
export interface City {
  id: string;
  name: string;
  slug: string;
  country: string;
  state?: string;
  description: string;
  overview: string;
  images: CityImage[];
  coordinates: {
    latitude: number;
    longitude: number;
  };
  population?: number;
  area?: number;
  timezone: string;
  currency: string;
  languages: string[];
  climate: {
    type: string;
    bestTimeToVisit: string;
    averageTemperature: {
      summer: number;
      winter: number;
    };
  };
  transportation: {
    howToReach: {
      byAir: string;
      byRoad: string;
      byRail: string;
    };
    localTransport: string[];
    airports: {
      name: string;
      code: string;
      distance: number;
    }[];
  };
  economy: {
    majorIndustries: string[];
    gdp?: number;
  };
  culture: {
    festivals: string[];
    traditions: string[];
    artAndCrafts: string[];
  };
  places: string[] | Place[];
  averageRating: number;
  totalReviews: number;
  isPublished: boolean;
  isFeatured: boolean;
  tags: string[];
  seoMetadata: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdBy: string | User;
  lastUpdatedBy: string | User;
  createdAt: string;
  updatedAt: string;
  url?: string;
}

export interface CityImage {
  url: string;
  alt: string;
  caption?: string;
  isPrimary: boolean;
}

// Place Types
export interface Place {
  id: string;
  name: string;
  slug: string;
  city: string | City;
  category: PlaceCategory;
  subcategory?: string;
  description: string;
  images: PlaceImage[];
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address: {
    street?: string;
    area: string;
    city: string;
    state?: string;
    country: string;
    postalCode?: string;
  };
  contact: {
    phone?: string;
    email?: string;
    website?: string;
  };
  timings: {
    openingHours: {
      [key: string]: { open: string; close: string; isClosed: boolean };
    };
    specialHours?: {
      date: string;
      open: string;
      close: string;
      note: string;
    }[];
  };
  pricing: {
    entryFee?: {
      adult: number;
      child: number;
      senior: number;
      currency: string;
    };
    priceRange?: 'budget' | 'moderate' | 'expensive' | 'luxury';
    averageCost?: number;
  };
  features: string[];
  cuisine?: string[];
  historicalSignificance?: {
    period: string;
    significance: string;
    historicalFacts: string[];
  };
  reviews: Review[];
  averageRating: number;
  totalReviews: number;
  popularityScore: number;
  isVerified: boolean;
  isPublished: boolean;
  isFeatured: boolean;
  tags: string[];
  seoMetadata: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdBy: string | User;
  lastUpdatedBy: string | User;
  createdAt: string;
  updatedAt: string;
  url?: string;
}

export interface PlaceImage {
  url: string;
  alt: string;
  caption?: string;
  isPrimary: boolean;
}

export type PlaceCategory = 
  | 'historical' 
  | 'attraction' 
  | 'restaurant' 
  | 'hotel' 
  | 'shopping' 
  | 'entertainment' 
  | 'nature' 
  | 'religious' 
  | 'museum' 
  | 'other';

// Review Types
export interface Review {
  id: string;
  user: string | User;
  rating: number;
  comment: string;
  images?: string[];
  createdAt: string;
  isVerified: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: any[];
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

// Specific response types for cities and places
export interface CitiesResponse {
  success: boolean;
  data: {
    cities: City[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
  error?: string;
}

export interface PlacesResponse {
  success: boolean;
  data: {
    places: Place[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
  error?: string;
}

export interface FeaturedCitiesResponse {
  success: boolean;
  data: {
    cities: City[];
  };
  error?: string;
}

// Search Types
export interface SearchResult {
  cities: City[];
  places: Place[];
  total: number;
}

export interface SearchSuggestion {
  cities: Pick<City, 'id' | 'name' | 'slug' | 'country'>[];
  places: Pick<Place, 'id' | 'name' | 'slug' | 'category'>[];
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// Filter Types
export interface CityFilters {
  country?: string;
  search?: string;
  minRating?: number;
  sort?: string;
  page?: number;
  limit?: number;
}

export interface PlaceFilters {
  city?: string;
  category?: PlaceCategory;
  search?: string;
  minRating?: number;
  priceRange?: string;
  sort?: string;
  page?: number;
  limit?: number;
}
