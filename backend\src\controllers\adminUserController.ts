import { Response } from 'express';
import User from '../models/User';
import { asyncHandler } from '../middleware/errorHandler';
import { AdminAuthRequest } from './adminAuthController';

// @desc    Get all users with pagination and filtering
// @route   GET /api/admin/users
// @access  Private (Admin)
export const getUsers = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;
  const role = req.query.role as string;
  const isActive = req.query.isActive as string;
  const sortBy = req.query.sortBy as string || 'createdAt';
  const sortOrder = req.query.sortOrder as string || 'desc';

  // Build filter object
  const filter: any = {};

  if (search) {
    filter.$or = [
      { name: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } }
    ];
  }

  if (role) {
    filter.role = role;
  }

  if (isActive !== undefined) {
    filter.isActive = isActive === 'true';
  }

  // Build sort object
  const sort: any = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Get users with pagination
  const users = await User.find(filter)
    .select('-password')
    .sort(sort)
    .skip(skip)
    .limit(limit)
    .populate('bookmarkedCities', 'name slug')
    .populate('bookmarkedPlaces', 'name slug');

  // Get total count for pagination
  const total = await User.countDocuments(filter);
  const pages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      items: users,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    }
  });
});

// @desc    Get single user
// @route   GET /api/admin/users/:id
// @access  Private (Admin)
export const getUser = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const user = await User.findById(req.params.id)
    .select('-password')
    .populate('bookmarkedCities', 'name slug country')
    .populate('bookmarkedPlaces', 'name slug category')
    .populate('contributedPlaces', 'name slug category')
    .populate('visitedPlaces.place', 'name slug category');

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  res.json({
    success: true,
    data: user
  });
});

// @desc    Update user
// @route   PUT /api/admin/users/:id
// @access  Private (Admin)
export const updateUser = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { name, email, role, isActive, profile, preferences } = req.body;

  const user = await User.findById(req.params.id);

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Update fields
  if (name) user.name = name;
  if (email) user.email = email;
  if (role) user.role = role;
  if (isActive !== undefined) user.isActive = isActive;
  if (profile) user.profile = { ...user.profile, ...profile };
  if (preferences) user.preferences = { ...user.preferences, ...preferences };

  await user.save();

  res.json({
    success: true,
    data: user
  });
});

// @desc    Delete user
// @route   DELETE /api/admin/users/:id
// @access  Private (Super Admin)
export const deleteUser = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  await User.findByIdAndDelete(req.params.id);

  res.json({
    success: true,
    message: 'User deleted successfully'
  });
});

// @desc    Toggle user active status
// @route   PATCH /api/admin/users/:id/toggle-status
// @access  Private (Admin)
export const toggleUserStatus = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  user.isActive = !user.isActive;
  await user.save();

  res.json({
    success: true,
    data: user,
    message: `User ${user.isActive ? 'activated' : 'deactivated'} successfully`
  });
});

// @desc    Bulk delete users
// @route   POST /api/admin/users/bulk-delete
// @access  Private (Super Admin)
export const bulkDeleteUsers = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Please provide an array of user IDs'
    });
  }

  const result = await User.deleteMany({ _id: { $in: ids } });

  res.json({
    success: true,
    message: `${result.deletedCount} users deleted successfully`
  });
});

// @desc    Get user statistics
// @route   GET /api/admin/users/stats
// @access  Private (Admin)
export const getUserStats = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const totalUsers = await User.countDocuments();
  const activeUsers = await User.countDocuments({ isActive: true });
  const inactiveUsers = await User.countDocuments({ isActive: false });
  
  // Users registered in the last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentUsers = await User.countDocuments({ 
    createdAt: { $gte: thirtyDaysAgo } 
  });

  // Users by role
  const usersByRole = await User.aggregate([
    {
      $group: {
        _id: '$role',
        count: { $sum: 1 }
      }
    }
  ]);

  res.json({
    success: true,
    data: {
      totalUsers,
      activeUsers,
      inactiveUsers,
      recentUsers,
      usersByRole
    }
  });
});
