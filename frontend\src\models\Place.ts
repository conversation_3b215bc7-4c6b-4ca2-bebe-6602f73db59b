import mongoose, { Document, Schema } from 'mongoose';

export interface IPlace extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  slug: string;
  city: mongoose.Types.ObjectId;
  category: 'historical' | 'attraction' | 'restaurant' | 'hotel' | 'shopping' | 'entertainment' | 'nature' | 'religious' | 'museum' | 'other';
  subcategory?: string;
  description: string;
  images: {
    url: string;
    alt: string;
    caption?: string;
    isPrimary: boolean;
  }[];
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address: {
    street?: string;
    area: string;
    city: string;
    state?: string;
    country: string;
    postalCode?: string;
  };
  contact: {
    phone?: string;
    email?: string;
    website?: string;
  };
  timings: {
    openingHours: {
      monday: { open: string; close: string; isClosed: boolean };
      tuesday: { open: string; close: string; isClosed: boolean };
      wednesday: { open: string; close: string; isClosed: boolean };
      thursday: { open: string; close: string; isClosed: boolean };
      friday: { open: string; close: string; isClosed: boolean };
      saturday: { open: string; close: string; isClosed: boolean };
      sunday: { open: string; close: string; isClosed: boolean };
    };
    specialHours?: {
      date: Date;
      open: string;
      close: string;
      note: string;
    }[];
  };
  pricing: {
    entryFee?: {
      adult: number;
      child: number;
      senior: number;
      currency: string;
    };
    priceRange?: 'budget' | 'moderate' | 'expensive' | 'luxury';
    averageCost?: number;
  };
  features: string[]; // WiFi, Parking, Wheelchair Accessible, etc.
  cuisine?: string[]; // For restaurants
  historicalSignificance?: {
    period: string;
    significance: string;
    historicalFacts: string[];
  };
  reviews: {
    user: mongoose.Types.ObjectId;
    rating: number;
    comment: string;
    images?: string[];
    createdAt: Date;
    isVerified: boolean;
  }[];
  averageRating: number;
  totalReviews: number;
  popularityScore: number; // Algorithm-based score
  isVerified: boolean; // Verified by admin/moderator
  isPublished: boolean;
  isFeatured: boolean;
  tags: string[];
  seoMetadata: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdBy: mongoose.Types.ObjectId;
  lastUpdatedBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const placeSchema = new Schema<IPlace>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  slug: {
    type: String,
    required: true,
    lowercase: true,
    trim: true
  },
  city: {
    type: Schema.Types.ObjectId,
    ref: 'City',
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['historical', 'attraction', 'restaurant', 'hotel', 'shopping', 'entertainment', 'nature', 'religious', 'museum', 'other']
  },
  subcategory: {
    type: String,
    trim: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 3000
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      required: true
    },
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  coordinates: {
    latitude: {
      type: Number,
      required: true,
      min: -90,
      max: 90
    },
    longitude: {
      type: Number,
      required: true,
      min: -180,
      max: 180
    }
  },
  address: {
    street: String,
    area: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    state: String,
    country: {
      type: String,
      required: true
    },
    postalCode: String
  },
  contact: {
    phone: String,
    email: String,
    website: String
  },
  timings: {
    openingHours: {
      monday: { open: String, close: String, isClosed: { type: Boolean, default: false } },
      tuesday: { open: String, close: String, isClosed: { type: Boolean, default: false } },
      wednesday: { open: String, close: String, isClosed: { type: Boolean, default: false } },
      thursday: { open: String, close: String, isClosed: { type: Boolean, default: false } },
      friday: { open: String, close: String, isClosed: { type: Boolean, default: false } },
      saturday: { open: String, close: String, isClosed: { type: Boolean, default: false } },
      sunday: { open: String, close: String, isClosed: { type: Boolean, default: false } }
    },
    specialHours: [{
      date: Date,
      open: String,
      close: String,
      note: String
    }]
  },
  pricing: {
    entryFee: {
      adult: Number,
      child: Number,
      senior: Number,
      currency: String
    },
    priceRange: {
      type: String,
      enum: ['budget', 'moderate', 'expensive', 'luxury']
    },
    averageCost: Number
  },
  features: [String],
  cuisine: [String],
  historicalSignificance: {
    period: String,
    significance: String,
    historicalFacts: [String]
  },
  reviews: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      required: true,
      maxlength: 1000
    },
    images: [String],
    createdAt: {
      type: Date,
      default: Date.now
    },
    isVerified: {
      type: Boolean,
      default: false
    }
  }],
  averageRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  totalReviews: {
    type: Number,
    default: 0,
    min: 0
  },
  popularityScore: {
    type: Number,
    default: 0
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  seoMetadata: {
    title: {
      type: String,
      required: true,
      maxlength: 60
    },
    description: {
      type: String,
      required: true,
      maxlength: 160
    },
    keywords: [String]
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastUpdatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound index for city and slug
placeSchema.index({ city: 1, slug: 1 }, { unique: true });

// Other indexes
placeSchema.index({ coordinates: '2dsphere' });
placeSchema.index({ category: 1 });
placeSchema.index({ name: 'text', description: 'text' });
placeSchema.index({ isPublished: 1, isVerified: 1 });
placeSchema.index({ averageRating: -1 });
placeSchema.index({ popularityScore: -1 });

// Virtual for URL
placeSchema.virtual('url').get(function() {
  return `/places/${this.slug}`;
});

export default mongoose.model<IPlace>('Place', placeSchema);
