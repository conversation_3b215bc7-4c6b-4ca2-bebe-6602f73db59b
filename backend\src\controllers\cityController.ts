import { Response } from 'express';
import { validationResult } from 'express-validator';
import City from '../models/City';
import Place from '../models/Place';
import User from '../models/User';
import Review from '../models/Review';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

// @desc    Get all cities
// @route   GET /api/cities
// @access  Public
export const getCities = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const skip = (page - 1) * limit;

  // Build query
  const query: any = { isPublished: true };
  
  if (req.query.country) {
    query.country = new RegExp(req.query.country as string, 'i');
  }
  
  if (req.query.search) {
    query.$text = { $search: req.query.search as string };
  }

  // Build sort
  let sort: any = { createdAt: -1 };
  if (req.query.sort) {
    const sortField = req.query.sort as string;
    sort = {};
    if (sortField.startsWith('-')) {
      sort[sortField.substring(1)] = -1;
    } else {
      sort[sortField] = 1;
    }
  }

  const cities = await City.find(query)
    .sort(sort)
    .skip(skip)
    .limit(limit)
    .populate('createdBy', 'name')
    .select('-__v');

  const total = await City.countDocuments(query);

  res.json({
    success: true,
    data: {
      cities,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get single city
// @route   GET /api/cities/:slug
// @access  Public
export const getCity = asyncHandler(async (req: AuthRequest, res: Response) => {
  const city = await City.findOne({ slug: req.params.slug, isPublished: true })
    .populate('places')
    .populate('createdBy', 'name avatar');

  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  res.json({
    success: true,
    data: {
      city
    }
  });
});

// @desc    Create city
// @route   POST /api/cities
// @access  Private (Admin/Moderator)
export const createCity = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  // Generate slug from name
  const slug = req.body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
  
  const cityData = {
    ...req.body,
    slug,
    createdBy: req.user!._id,
    lastUpdatedBy: req.user!._id
  };

  const city = await City.create(cityData);

  res.status(201).json({
    success: true,
    message: 'City created successfully',
    data: {
      city
    }
  });
});

// Placeholder implementations for other methods
export const updateCity = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Update city - not implemented yet' });
});

export const deleteCity = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Delete city - not implemented yet' });
});

export const getFeaturedCities = asyncHandler(async (req: AuthRequest, res: Response) => {
  const cities = await City.find({ isPublished: true, isFeatured: true })
    .limit(6)
    .sort({ averageRating: -1 });

  res.json({
    success: true,
    data: { cities }
  });
});

// @desc    Get global statistics
// @route   GET /api/cities/stats/global
// @access  Public
export const getGlobalStats = asyncHandler(async (req: AuthRequest, res: Response) => {
  try {
    // Get total number of users (happy travellers)
    const totalUsers = await User.countDocuments({});

    // Get total number of cities
    const totalCities = await City.countDocuments({ isPublished: true });

    // Get total number of places
    const totalPlaces = await Place.countDocuments({ isPublished: true });

    // Calculate average rating across all places
    const places = await Place.find({ isPublished: true, averageRating: { $gt: 0 } });
    const averageRating = places.length > 0
      ? places.reduce((sum, place) => sum + place.averageRating, 0) / places.length
      : 0;

    res.json({
      success: true,
      data: {
        happyTravellers: totalUsers,
        citiesCovered: totalCities,
        placesListed: totalPlaces,
        averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
      }
    });
  } catch (error) {
    console.error('Error getting global stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get global statistics'
    });
  }
});

export const getCityPlaces = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { slug } = req.params;
  const { category, page = 1, limit = 20 } = req.query;

  // Find the city first
  const city = await City.findOne({ slug, isPublished: true });
  if (!city) {
    return res.status(404).json({
      success: false,
      message: 'City not found'
    });
  }

  // Build query for places
  const query: any = {
    city: city._id,
    isPublished: true
  };

  if (category) {
    query.category = category;
  }

  // Calculate pagination
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  // Get places with pagination
  const places = await Place.find(query)
    .populate('city', 'name slug')
    .sort({ popularityScore: -1, averageRating: -1 })
    .skip(skip)
    .limit(limitNum);

  // Get total count for pagination
  const total = await Place.countDocuments(query);
  const totalPages = Math.ceil(total / limitNum);

  res.json({
    success: true,
    data: {
      places,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1
      }
    }
  });
});

export const bookmarkCity = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Bookmark city - not implemented yet' });
});

export const unbookmarkCity = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Unbookmark city - not implemented yet' });
});

export const getCityStats = asyncHandler(async (req: AuthRequest, res: Response) => {
  res.json({ success: true, message: 'Get city stats - not implemented yet' });
});

// @desc    Get city reviews
// @route   GET /api/cities/:slug/reviews
// @access  Public
export const getCityReviews = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { slug } = req.params;

  // Find the city
  const city = await City.findOne({ slug, isPublished: true });
  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Get reviews for this city (only approved reviews)
  const reviews = await Review.find({
    entityType: 'city',
    entityId: city._id,
    isApproved: true
  })
  .populate('user', 'name avatar')
  .sort({ createdAt: -1 });

  res.json({
    success: true,
    data: reviews
  });
});

// @desc    Add review to city
// @route   POST /api/cities/:slug/reviews
// @access  Private
export const addCityReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { slug } = req.params;
  const { rating, comment } = req.body;

  // Find the city
  const city = await City.findOne({ slug, isPublished: true });
  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Check if user already reviewed this city
  const existingReview = await Review.findOne({
    entityType: 'city',
    entityId: city._id,
    user: req.user!._id
  });

  if (existingReview) {
    return res.status(400).json({
      success: false,
      error: 'You have already reviewed this city'
    });
  }

  // Create review
  const review = await Review.create({
    entityType: 'city',
    entityId: city._id,
    user: req.user!._id,
    rating,
    comment
  });

  // Populate user data
  await review.populate('user', 'name avatar');

  // Update city's average rating (only approved reviews)
  const reviews = await Review.find({
    entityType: 'city',
    entityId: city._id,
    isApproved: true
  });

  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
    : 0;
  await City.findByIdAndUpdate(city._id, {
    averageRating,
    totalReviews: reviews.length
  });

  res.status(201).json({
    success: true,
    message: 'Review added successfully',
    data: review
  });
});

// @desc    Update city review
// @route   PUT /api/cities/:slug/reviews/:reviewId
// @access  Private (Review owner only)
export const updateCityReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { slug, reviewId } = req.params;
  const { rating, comment } = req.body;

  // Find the city
  const city = await City.findOne({ slug, isPublished: true });
  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Find the review
  const review = await Review.findOne({
    _id: reviewId,
    entityType: 'city',
    entityId: city._id
  });

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  // Check if user owns the review
  if (review.user.toString() !== req.user!._id.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to update this review'
    });
  }

  // Update review
  review.rating = rating || review.rating;
  review.comment = comment || review.comment;
  await review.save();

  // Populate user data
  await review.populate('user', 'name avatar');

  // Update city's average rating (only approved reviews)
  const reviews = await Review.find({
    entityType: 'city',
    entityId: city._id,
    isApproved: true
  });

  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
    : 0;
  await City.findByIdAndUpdate(city._id, {
    averageRating,
    totalReviews: reviews.length
  });

  res.json({
    success: true,
    message: 'Review updated successfully',
    data: review
  });
});

// @desc    Delete city review
// @route   DELETE /api/cities/:slug/reviews/:reviewId
// @access  Private (Review owner, Admin, or Moderator)
export const deleteCityReview = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { slug, reviewId } = req.params;

  // Find the city
  const city = await City.findOne({ slug, isPublished: true });
  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Find the review
  const review = await Review.findOne({
    _id: reviewId,
    entityType: 'city',
    entityId: city._id
  });

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  // Check if user owns the review or is admin/moderator
  const isOwner = review.user.toString() === req.user!._id.toString();
  const isAdminOrModerator = req.user!.role === 'admin' || req.user!.role === 'moderator';

  if (!isOwner && !isAdminOrModerator) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to delete this review'
    });
  }

  // Delete review
  await Review.findByIdAndDelete(reviewId);

  // Update city's average rating (only approved reviews)
  const reviews = await Review.find({
    entityType: 'city',
    entityId: city._id,
    isApproved: true
  });

  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
    : 0;

  await City.findByIdAndUpdate(city._id, {
    averageRating,
    totalReviews: reviews.length
  });

  res.json({
    success: true,
    message: 'Review deleted successfully'
  });
});
