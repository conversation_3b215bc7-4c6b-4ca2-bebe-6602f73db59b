import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import Admin, { IAdmin } from '../models/Admin';

export interface AdminAuthRequest extends Request {
  admin?: IAdmin;
}

// Generate JWT Token
const generateToken = (id: string): string => {
  const secret = process.env.JWT_SECRET || 'fallback_secret';
  return jwt.sign({ id, type: 'admin' }, secret, {
    expiresIn: '24h'
  });
};

// @desc    Admin login
// @route   POST /api/admin/auth/login
// @access  Public
export const adminLogin = asyncHandler(async (req: Request, res: Response) => {
  const { email, password } = req.body;

  // Validate email and password
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      error: 'Please provide email and password'
    });
  }

  // Check for admin user
  const admin = await Admin.findOne({ email }).select('+password');

  if (!admin) {
    return res.status(401).json({
      success: false,
      error: 'Invalid credentials'
    });
  }

  // Check if account is locked
  const isLocked = admin.lockUntil && admin.lockUntil > new Date();
  if (isLocked) {
    return res.status(423).json({
      success: false,
      error: 'Account temporarily locked due to too many failed login attempts'
    });
  }

  // Check if account is active
  if (!admin.isActive) {
    return res.status(401).json({
      success: false,
      error: 'Account has been deactivated'
    });
  }

  // Check if password matches
  const isMatch = await admin.comparePassword(password);

  if (!isMatch) {
    // Increment login attempts
    await admin.incLoginAttempts();
    return res.status(401).json({
      success: false,
      error: 'Invalid credentials'
    });
  }

  // Reset login attempts on successful login
  await admin.resetLoginAttempts();

  // Create token
  const token = generateToken(admin._id);

  res.json({
    success: true,
    data: {
      token,
      admin: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        role: admin.role,
        avatar: admin.avatar,
        lastLoginAt: admin.lastLoginAt
      }
    }
  });
});

// @desc    Get current admin
// @route   GET /api/admin/auth/me
// @access  Private (Admin)
export const getAdminProfile = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const admin = await Admin.findById(req.admin!._id);

  if (!admin) {
    return res.status(404).json({
      success: false,
      error: 'Admin not found'
    });
  }

  res.json({
    success: true,
    data: {
      admin: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        role: admin.role,
        avatar: admin.avatar,
        lastLoginAt: admin.lastLoginAt,
        createdAt: admin.createdAt
      }
    }
  });
});

// @desc    Update admin profile
// @route   PUT /api/admin/auth/profile
// @access  Private (Admin)
export const updateAdminProfile = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { name, email, avatar } = req.body;

  const admin = await Admin.findById(req.admin!._id);

  if (!admin) {
    return res.status(404).json({
      success: false,
      error: 'Admin not found'
    });
  }

  // Update fields
  if (name) admin.name = name;
  if (email) admin.email = email;
  if (avatar !== undefined) admin.avatar = avatar;

  await admin.save();

  res.json({
    success: true,
    data: {
      admin: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        role: admin.role,
        avatar: admin.avatar,
        lastLoginAt: admin.lastLoginAt
      }
    }
  });
});

// @desc    Change admin password
// @route   PUT /api/admin/auth/password
// @access  Private (Admin)
export const changeAdminPassword = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      error: 'Please provide current password and new password'
    });
  }

  const admin = await Admin.findById(req.admin!._id).select('+password');

  if (!admin) {
    return res.status(404).json({
      success: false,
      error: 'Admin not found'
    });
  }

  // Check current password
  const isMatch = await admin.comparePassword(currentPassword);

  if (!isMatch) {
    return res.status(400).json({
      success: false,
      error: 'Current password is incorrect'
    });
  }

  // Validate new password
  if (newPassword.length < 8) {
    return res.status(400).json({
      success: false,
      error: 'New password must be at least 8 characters'
    });
  }

  // Update password
  admin.password = newPassword;
  await admin.save();

  res.json({
    success: true,
    message: 'Password updated successfully'
  });
});

// @desc    Admin logout
// @route   POST /api/admin/auth/logout
// @access  Private (Admin)
export const adminLogout = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});
