const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge';

async function addSimplePlaces() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    // Get the cities collection
    const citiesCollection = mongoose.connection.db.collection('cities');
    const placesCollection = mongoose.connection.db.collection('places');

    // Find Paris city
    const parisCity = await citiesCollection.findOne({ slug: 'paris' });
    if (!parisCity) {
      console.log('❌ Paris city not found');
      return;
    }

    console.log(`✅ Found Paris city: ${parisCity.name} (ID: ${parisCity._id})`);

    // Clear existing places
    await placesCollection.deleteMany({});
    console.log('🗑️ Cleared existing places');

    // Add simple test places
    const testPlaces = [
      {
        name: 'Eiffel Tower',
        slug: 'eiffel-tower',
        city: parisCity._id,
        category: 'attraction',
        subcategory: 'landmark',
        description: 'The iconic iron lattice tower on the Champ de Mars in Paris, France.',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1511739001486-6bfe10ce785f?auto=format&fit=crop&w=800&q=80',
            alt: 'Eiffel Tower at sunset',
            caption: 'The iconic Eiffel Tower illuminated at sunset',
            isPrimary: true
          }
        ],
        coordinates: {
          latitude: 48.8584,
          longitude: 2.2945
        },
        address: {
          street: 'Champ de Mars',
          area: '7th arrondissement',
          city: 'Paris',
          country: 'France',
          postalCode: '75007'
        },
        contact: {
          website: 'https://www.toureiffel.paris'
        },
        pricing: {
          entryFee: {
            adult: 29.40,
            child: 14.70,
            currency: 'EUR'
          }
        },
        features: ['elevator', 'stairs', 'restaurant', 'gift shop', 'observation deck'],
        averageRating: 4.6,
        totalReviews: 15420,
        popularityScore: 95,
        isVerified: true,
        isPublished: true,
        isFeatured: true,
        tags: ['iconic', 'landmark', 'tower', 'views', 'romantic'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Louvre Museum',
        slug: 'louvre-museum',
        city: parisCity._id,
        category: 'museum',
        subcategory: 'art museum',
        description: 'The world\'s largest art museum and a historic monument in Paris, France.',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1566139884669-4b9356b4c040?auto=format&fit=crop&w=800&q=80',
            alt: 'Louvre Museum pyramid',
            caption: 'The iconic glass pyramid entrance to the Louvre Museum',
            isPrimary: true
          }
        ],
        coordinates: {
          latitude: 48.8606,
          longitude: 2.3376
        },
        address: {
          street: 'Rue de Rivoli',
          area: '1st arrondissement',
          city: 'Paris',
          country: 'France',
          postalCode: '75001'
        },
        contact: {
          website: 'https://www.louvre.fr'
        },
        pricing: {
          entryFee: {
            adult: 17.00,
            child: 0.00,
            currency: 'EUR'
          }
        },
        features: ['audio guide', 'gift shop', 'restaurant', 'wheelchair accessible'],
        averageRating: 4.5,
        totalReviews: 8932,
        popularityScore: 90,
        isVerified: true,
        isPublished: true,
        isFeatured: true,
        tags: ['art', 'museum', 'mona lisa', 'culture', 'history'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Notre-Dame Cathedral',
        slug: 'notre-dame-cathedral',
        city: parisCity._id,
        category: 'religious',
        subcategory: 'cathedral',
        description: 'A medieval Catholic cathedral on the Île de la Cité in the 4th arrondissement of Paris.',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?auto=format&fit=crop&w=800&q=80',
            alt: 'Notre-Dame Cathedral facade',
            caption: 'The magnificent Gothic facade of Notre-Dame Cathedral',
            isPrimary: true
          }
        ],
        coordinates: {
          latitude: 48.8530,
          longitude: 2.3499
        },
        address: {
          street: '6 Parvis Notre-Dame',
          area: '4th arrondissement',
          city: 'Paris',
          country: 'France',
          postalCode: '75004'
        },
        contact: {
          website: 'https://www.notredamedeparis.fr'
        },
        pricing: {
          entryFee: {
            adult: 0.00,
            child: 0.00,
            currency: 'EUR'
          }
        },
        features: ['free entry', 'guided tours', 'religious services'],
        averageRating: 4.4,
        totalReviews: 12543,
        popularityScore: 85,
        isVerified: true,
        isPublished: true,
        isFeatured: true,
        tags: ['gothic', 'cathedral', 'religious', 'architecture', 'history'],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insert places
    const result = await placesCollection.insertMany(testPlaces);
    console.log(`✅ Successfully inserted ${result.insertedCount} places`);

    // Verify
    const totalPlaces = await placesCollection.countDocuments();
    console.log(`📊 Total places in database: ${totalPlaces}`);

    // List places
    const places = await placesCollection.find({}).toArray();
    console.log('\n📍 Places added:');
    places.forEach(place => {
      console.log(`  - ${place.name} (${place.category})`);
    });

    console.log('\n🎉 Places import completed successfully!');
    console.log('🌐 You can now view places at: http://localhost:3001/cities/paris');

  } catch (error) {
    console.error('❌ Failed to add places:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  addSimplePlaces();
}

module.exports = { addSimplePlaces };
