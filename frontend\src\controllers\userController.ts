import { Response } from 'express';
import { validationResult } from 'express-validator';
import User from '../models/User';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

// @desc    Get user profile
// @route   GET /api/users/:userId
// @access  Public (if profile is public) / Private (own profile)
export const getUserProfile = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const user = await User.findById(req.params.userId)
    .populate('contributedPlaces', 'name slug images averageRating')
    .select('-email -bookmarkedCities -bookmarkedPlaces');

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Check if profile is public or if it's the user's own profile
  const isOwnProfile = req.user && req.user._id.toString() === user._id.toString();
  
  if (!user.preferences.publicProfile && !isOwnProfile) {
    return res.status(403).json({
      success: false,
      error: 'This profile is private'
    });
  }

  res.json({
    success: true,
    data: {
      user
    }
  });
});

// @desc    Get user bookmarks
// @route   GET /api/users/:userId/bookmarks
// @access  Private (own bookmarks only)
export const getUserBookmarks = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  // Check if user is accessing their own bookmarks
  if (req.user!._id.toString() !== req.params.userId) {
    return res.status(403).json({
      success: false,
      error: 'You can only access your own bookmarks'
    });
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const type = req.query.type as string || 'all';

  const user = await User.findById(req.params.userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  let bookmarks: any = {};

  if (type === 'cities' || type === 'all') {
    const cityBookmarks = await User.findById(req.params.userId)
      .populate({
        path: 'bookmarkedCities',
        select: 'name slug description images averageRating country',
        options: {
          skip: (page - 1) * limit,
          limit: limit
        }
      })
      .select('bookmarkedCities');
    
    bookmarks.cities = cityBookmarks?.bookmarkedCities || [];
  }

  if (type === 'places' || type === 'all') {
    const placeBookmarks = await User.findById(req.params.userId)
      .populate({
        path: 'bookmarkedPlaces',
        select: 'name slug description images averageRating category',
        populate: {
          path: 'city',
          select: 'name slug'
        },
        options: {
          skip: (page - 1) * limit,
          limit: limit
        }
      })
      .select('bookmarkedPlaces');
    
    bookmarks.places = placeBookmarks?.bookmarkedPlaces || [];
  }

  res.json({
    success: true,
    data: {
      bookmarks,
      pagination: {
        page,
        limit
      }
    }
  });
});

// @desc    Get user contributions
// @route   GET /api/users/:userId/contributions
// @access  Public (if profile is public) / Private (own contributions)
export const getUserContributions = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const user = await User.findById(req.params.userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Check if profile is public or if it's the user's own profile
  const isOwnProfile = req.user && req.user._id.toString() === user._id.toString();
  
  if (!user.preferences.publicProfile && !isOwnProfile) {
    return res.status(403).json({
      success: false,
      error: 'This profile is private'
    });
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  const contributions = await User.findById(req.params.userId)
    .populate({
      path: 'contributedPlaces',
      select: 'name slug description images averageRating category isPublished',
      populate: {
        path: 'city',
        select: 'name slug'
      },
      options: {
        skip: (page - 1) * limit,
        limit: limit,
        sort: { createdAt: -1 }
      }
    })
    .select('contributedPlaces');

  res.json({
    success: true,
    data: {
      contributions: contributions?.contributedPlaces || [],
      pagination: {
        page,
        limit
      }
    }
  });
});

// @desc    Update user preferences
// @route   PUT /api/users/:userId/preferences
// @access  Private (own preferences only)
export const updateUserPreferences = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  // Check if user is updating their own preferences
  if (req.user!._id.toString() !== req.params.userId) {
    return res.status(403).json({
      success: false,
      error: 'You can only update your own preferences'
    });
  }

  const allowedPreferences = ['newsletter', 'notifications', 'publicProfile'];
  const updates: any = {};

  Object.keys(req.body).forEach(key => {
    if (allowedPreferences.includes(key)) {
      updates[`preferences.${key}`] = req.body[key];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.params.userId,
    updates,
    { new: true, runValidators: true }
  );

  res.json({
    success: true,
    message: 'Preferences updated successfully',
    data: {
      preferences: user?.preferences
    }
  });
});
