# GitHub Actions CI/CD Setup

This directory contains GitHub Actions workflows for automated building and deployment of Docker images.

## Docker Build and Push Workflow

The `docker-build-push.yml` workflow automatically builds and pushes Docker images for all three services (admin, backend, frontend) to Docker Hub.

### Setup Instructions

#### 1. Docker Hub Configuration

1. **Create Docker Hub Account**: If you don't have one, create an account at [hub.docker.com](https://hub.docker.com)

2. **Generate Access Token**:
   - Go to Docker Hub → Account Settings → Security
   - Click "New Access Token"
   - Give it a descriptive name (e.g., "GitHub Actions Travelogix")
   - Copy the generated token

#### 2. GitHub Repository Secrets

Add the following secrets to your GitHub repository:

1. Go to your repository → Settings → Secrets and variables → Actions
2. Add these repository secrets:

   - **DOCKER_HUB_USERNAME**: Your Docker Hub username
   - **DOCKER_HUB_TOKEN**: The access token you generated above

#### 3. Update Workflow Configuration

In `.github/workflows/docker-build-push.yml`, update the Docker Hub username:

```yaml
env:
  DOCKER_HUB_USERNAME: ${{ secrets.DOCKER_HUB_USERNAME }}
```

### Workflow Triggers

The workflow runs on:

- **Push to main/develop branches**: Builds and pushes images with branch name tags
- **Pull requests**: Builds images but doesn't push (for testing)
- **Git tags (v*)**: Builds and pushes with semantic version tags
- **Manual trigger**: Can be triggered manually from GitHub Actions tab

### Generated Docker Images

The workflow creates the following images:

- `your-username/travelogix-admin:latest`
- `your-username/travelogix-backend:latest`
- `your-username/travelogix-frontend:latest`

### Image Tags

Images are tagged with:

- `latest` (for main branch)
- Branch name (for feature branches)
- Semantic version (for git tags like v1.0.0)
- PR number (for pull requests)

### Features

- **Multi-platform builds**: Supports both AMD64 and ARM64 architectures
- **Build caching**: Uses GitHub Actions cache for faster builds
- **Security scanning**: Includes Trivy vulnerability scanning
- **Matrix strategy**: Builds all services in parallel
- **Conditional pushing**: Only pushes on main branches, not PRs

### Usage Examples

#### Deploying with Docker Compose

Update your `docker-compose.yml` to use the pushed images:

```yaml
version: '3.8'
services:
  frontend:
    image: your-username/travelogix-frontend:latest
    ports:
      - "3000:3000"
  
  backend:
    image: your-username/travelogix-backend:latest
    ports:
      - "5000:5000"
  
  admin:
    image: your-username/travelogix-admin:latest
    ports:
      - "3001:3001"
```

#### Deploying to Kubernetes

Update your Kubernetes deployment files in the `k8s/` directory:

```yaml
spec:
  containers:
  - name: frontend
    image: your-username/travelogix-frontend:latest
```

### Monitoring

- Check the Actions tab in your GitHub repository to monitor build status
- Security scan results appear in the Security tab
- Build logs provide detailed information about each step

### Troubleshooting

1. **Authentication errors**: Verify Docker Hub credentials in repository secrets
2. **Build failures**: Check the Actions logs for specific error messages
3. **Permission issues**: Ensure your Docker Hub token has write permissions
