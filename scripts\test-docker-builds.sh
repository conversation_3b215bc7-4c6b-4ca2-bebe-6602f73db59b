#!/bin/bash

# Local Docker build testing script
# Tests all Docker builds locally before pushing to CI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo "🐳 Testing Docker Builds Locally"
echo "================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running"

# Services to build
SERVICES=("admin" "backend" "frontend")
FAILED_BUILDS=()

# Build each service
for service in "${SERVICES[@]}"; do
    echo ""
    print_info "Building $service service..."
    
    if [ ! -f "$service/Dockerfile" ]; then
        print_error "Dockerfile not found for $service service"
        FAILED_BUILDS+=("$service")
        continue
    fi
    
    # Build the Docker image
    if docker build -t "travelogix-$service:test" "$service/"; then
        print_status "$service build successful"
        
        # Test if the image was created
        if docker images "travelogix-$service:test" --format "table {{.Repository}}:{{.Tag}}" | grep -q "test"; then
            print_status "$service image created successfully"
        else
            print_error "$service image not found after build"
            FAILED_BUILDS+=("$service")
        fi
    else
        print_error "$service build failed"
        FAILED_BUILDS+=("$service")
    fi
done

echo ""
echo "📊 Build Summary"
echo "================"

if [ ${#FAILED_BUILDS[@]} -eq 0 ]; then
    print_status "All builds completed successfully! 🎉"
    echo ""
    print_info "Built images:"
    for service in "${SERVICES[@]}"; do
        echo "  • travelogix-$service:test"
    done
    
    echo ""
    print_info "Next steps:"
    echo "  • Run 'docker images' to see all built images"
    echo "  • Test individual services with 'docker run'"
    echo "  • Push your changes to trigger CI/CD pipeline"
    
    echo ""
    read -p "Do you want to clean up test images? (y/N): " cleanup
    if [[ $cleanup =~ ^[Yy]$ ]]; then
        echo ""
        print_info "Cleaning up test images..."
        for service in "${SERVICES[@]}"; do
            if docker rmi "travelogix-$service:test" > /dev/null 2>&1; then
                print_status "Removed travelogix-$service:test"
            fi
        done
    fi
    
else
    print_error "Some builds failed:"
    for service in "${FAILED_BUILDS[@]}"; do
        echo "  • $service"
    done
    echo ""
    print_warning "Please fix the build issues before pushing to CI/CD"
    exit 1
fi

echo ""
print_status "Local testing completed!"
